from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import StreamingResponse, HTMLResponse
from services import AI_essay_answer_point, AI_essay_correction, AI_essay_location


# EssayCorrection = FastAPI(lifespan=lifespan)
EssayCorrection = FastAPI()

# 添加 CORS 支持
# origins = [
#     "http://127.0.0.1:8000",
#     "http://localhost:8000",
# ]

# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

@EssayCorrection.get("/")
async def index():
    with open("./static/index.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content, status_code=200)


@EssayCorrection.get("/correction")
async def index_correction():
    with open("./static/indexCorrection.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content, status_code=200)


@EssayCorrection.get("/point")
async def index_point():
    with open("./static/indexPoint.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content, status_code=200)


@EssayCorrection.get("/check/status")
async def check_status():
    return {"status": "ok"}


# 要点定位
@EssayCorrection.post("/location/analysis")
async def analysis(request: Request):
    try:
        body = await request.json()
    except Exception as e:
        return StreamingResponse(f"请求内容格式有误，{e}", media_type="text/event-stream")
    
    return StreamingResponse(AI_essay_location.run(request, body), media_type="text/event-stream")


# 要点批改
@EssayCorrection.post("/correction/analysis")
async def correction_analysis(request: Request):
    try:
        body = await request.json()
    except Exception as e:
        return StreamingResponse(f"请求内容格式有误，{e}", media_type="text/event-stream")

    # 调用异步运行函数
    result = await AI_essay_correction.run(body, request)
    return result


# 要点生成
@EssayCorrection.post("/point/analysis")
async def point_analysis(request: Request):
    try:
        body = await request.json()
    except Exception as e:
        return StreamingResponse(f"请求内容格式有误，{e}", media_type="text/event-stream")

    # 调用异步运行函数
    result = await AI_essay_answer_point.run(body)
    return result