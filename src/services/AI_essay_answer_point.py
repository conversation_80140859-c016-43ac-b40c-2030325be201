import re
import json
from services.prompt import *
from datetime import datetime
from utils.score_tools import *
from utils.error_codes import ErrorClassifier
from services.model_client import ModelClient
from utils.validator import check_answer_point_structure


async def get_answer_point_essential_data(data):
    """
    获取答案要点必要数据
    """
    answer_requirement = re.sub(r"[（(]*\d+(\.\d+)?分[）)]*", "", data['answerRequirement'])
    keyPoint = {
        "scoreForModel": f"{data['contentScore']}分",
        "score": data['contentScore'],
        "content": data['answer'],
        "answerRequirement": answer_requirement,
    }
    data['keyPoint'] = keyPoint
    return data


async def run(data):
    start_time = datetime.now()
    try:
        # 校验数据结构，如果数据结构有误，则返回错误原因
        is_pass, wrong_text = check_answer_point_structure(data)
        if not is_pass:
            raise Exception(f"数据结构有误: {wrong_text}")
        
        log_prefix = f"question_id={data['questionId']}, "
        logger.info(f"{log_prefix}start answer point generation, data={json.dumps(data, ensure_ascii=False)}")
        
        model_cls = ModelClient(log_prefix)

        # 获取必要数据
        data = await get_answer_point_essential_data(data)

        # 如果是应用文，“答案”先过{格式处理}提示词，只获取“正文”的内容作为答案内容。
        if data["questionType"] == 4:
            format_result = await model_cls.get_answer_point_format_result(model_cls, data, 0)
            data["keyPoint"]["content"] = format_result["正文"]
        
        answer_points_result = await model_cls.get_answer_point_result(model_cls, data, 0)

        # 验证答案要点总分是否等于题目总分
        answer_points = answer_points_result["答案要点"]
        total_score = sum(point["要点分数"] for point in answer_points)
        expected_score = data["keyPoint"]["score"]
        
        # 如果总分不匹配，进行二次校正
        if total_score != expected_score:
            logger.warning(f"{log_prefix}answer points total score not match, expected: {expected_score}, actual: {total_score}, do correction")
            
            # 二次校正
            answer_points_correction_result = await model_cls.get_answer_point_correction_result(model_cls, data, answer_points, 0)
            answer_points = answer_points_correction_result["答案要点"]
            total_score = sum(point["要点分数"] for point in answer_points)
            if total_score != expected_score:
                logger.error(f"{log_prefix}correction answer points total score not match, expected: {expected_score}, actual: {total_score}")
                raise Exception(f"要点分数与总分不符")
        
        # 将答案要点转换为返回格式
        answer_position = [
            {"content": point["要点"], "score": point["要点分数"]}
            for point in answer_points
        ]
        
        # 构建返回结果
        to_java_result = {
            "questionId": data["questionId"],
            "status": 1,  # 1成功 2失败
            "answerPosition": answer_position,
            "message": "",  # 失败原因，成功时为空
            "errorCode": None,  # 错误码, 成功时为None
            "timestamp": data.get("timestamp")  # 原样返回
        }            
    except Exception as e:
        logger.error(f"answer point generation run error = {e}, request_data = {data}", exc_info=True)
        error_message = str(e)
        error_code = ErrorClassifier.get_error_code(error_message, "answer_point")
        # 构建失败返回结果
        to_java_result = {
            "questionId": data.get("questionId", 0),
            "status": 2,  # 1成功 2失败
            "answerPosition": [],
            "message": error_message,  # 失败原因
            "errorCode": error_code,  # 错误码
            "timestamp": data.get("timestamp")  # 原样返回
        }

    return to_java_result, datetime.now() - start_time