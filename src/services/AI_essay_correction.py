import re
import json
import asyncio
from services.prompt import *
from datetime import datetime
from utils.score_tools import *
from utils.error_codes import ErrorClassifier
from services.model_client import ModelClient
from services.image_service import ImageService
from services.analysis_manager import AnalysisManager
from utils.validator import check_correction_structure
from utils.general_tools import get_tokens, risk_assessment, remove_tags_bs4, slice_and_match, convert_chinese_punctuation_to_english


class KeyPointProcessor:
    """要点处理器"""

    def __init__(self, log_prefix=""):
        self.log_prefix = log_prefix
    
    def check_in_answer(self, data, line_result):
        try:
            need_gpt_list = []
            # 处理每个回答要点
            for point_id, item in enumerate(line_result):
                answer_point = convert_chinese_punctuation_to_english(item['回答要点'])
                if item['回答要点分类'] == '遗漏':
                    continue
                # 按照标点符号进行切片
                slices = separate_text_by_punctuation(answer_point)
                # 去除空字符串
                slices = [s.strip() for s in slices if s.strip()]
                matched, not_matched = slice_and_match(slices, data['userAnswer'])
                item['回答要点'] = ",".join(matched)
                if not_matched != []:
                    for i, s in enumerate(not_matched):
                        suffix = f"-{i + 1}" if len(not_matched) >= 2 else ""
                        need_gpt_list.append({f"要点{point_id + 1}{suffix}": f"{s}"})
            return need_gpt_list
        except Exception as e:
            logger.error(f"{self.log_prefix}check_in_answer error = {e}")
            return None

    async def get_score_count(self, data, line_result, logical_result, format_result):
        """
        通过要点扣分、逻辑扣分、格式扣分查看得分情况。step 4.
        通过要点扣分、逻辑扣分生成内容要点点评。step 5.
        通过要点扣分、逻辑扣分获得评语step 6
        """
        # 是否折算
        has_conversion = data['keyPoint']['hasConversion']
        # 工程侧给到总分
        total_score = data['keyPoint']['score'] if not has_conversion else data['keyPoint']['convertScore']
        # 获取折算比例
        conversion_ratio = data['keyPoint']['conversionRatio']
        
        # 初始化输出变量
        line_wrong, format_wrong, logical_wrong, student_score = 0, 0, 0, 0
        score_conclusion, key_point_review, comment_conclusion = '', '', ''
        
        try:
            # 计算格式扣分
            format_total_score, format_score = await check_format_score(data, format_result)
            if format_score > format_total_score:
                logger.warning(f"{self.log_prefix}format_score > format_total_score, format_score: {format_score}, format_total_score: {format_total_score}")
                format_score = format_total_score
            format_wrong = custom_round(format_total_score - format_score, 1)
            
            # 应用文逻辑扣分key与非应用文不一样
            logical_key = "逻辑层次" if data['questionType'] == 4 else "逻辑条理"
            # 计算逻辑扣分
            logical_total_score = data['keyPoint']['logicalTotalScore']
            logical_wrong = get_clear_score(logical_result[f'{logical_key}扣分'])
            if logical_wrong > logical_total_score:
                logger.warning(f"{self.log_prefix}logical_wrong > logical_total_score, logical_wrong: {logical_wrong}, logical_total_score: {logical_total_score}")
                logical_wrong = logical_total_score
                
            # 如果折算比例不为1，进行逻辑扣分的折算
            if has_conversion:
                convert_logical_total_score = data['keyPoint']['convertLogicalTotalScore']
                if logical_wrong == logical_total_score:
                    logical_wrong = convert_logical_total_score
                else:
                    logical_wrong = score.convert_score(logical_wrong, conversion_ratio, convert_logical_total_score)
            
            # 获取学生要点得分和点评
            line_score, key_point_review, keyPoint_lost = await check_score(data, line_result)
            # 计算要点总分(总分减去格式扣分)
            line_total_score = total_score - format_total_score
            # 计算要点扣分: 要点总分减去学生要点得分
            line_wrong = custom_round(line_total_score - line_score, 1)
            
            # 计算最终得分
            student_score = custom_round(total_score - line_wrong - logical_wrong - format_wrong, 1)
            student_score = 0 if student_score <= 0 else student_score
                        
            # 生成得分说明文本
            score_conclusion = "无扣分"

            # 构建扣分说明
            deduction_items = []
            
            if line_wrong > 0:
                deduction_items.append(f'遗漏及不准确要点扣<span style="color: red;">{line_wrong}</span>分')
            
            format_wrong_safe = custom_round(min(format_wrong, total_score - line_wrong), 1)
            if format_wrong_safe > 0:
                deduction_items.append(f'格式不准确扣<span style="color: red;">{format_wrong_safe}</span>分')

            logical_wrong_safe = custom_round(min(logical_wrong, total_score - line_wrong - format_wrong), 1)
            if logical_wrong_safe > 0:
                deduction_items.append(f'{logical_key}扣<span style="color: red;">{logical_wrong_safe}</span>分')
            
            # 连接扣分说明
            if deduction_items:
                score_conclusion = '，'.join(deduction_items) + '。'
            
            # 生成评语
            comment_conclusion = get_evaluation(total_score, student_score)
            
            result = {
                "遗漏及不准确要点扣分": line_wrong,
                "学生得分": student_score,
                "逻辑扣分": logical_wrong,
                "得分计算": score_conclusion.strip(),
                "内容要点点评": key_point_review.strip(),
                "评语": comment_conclusion.strip(),
                "关键词遗漏": str(keyPoint_lost).strip(),
            }
            # 应用文格式扣分
            if data['questionType'] == 4:
                result["格式不准确扣分"] = format_wrong

            # 记录分析日志
            AnalysisManager.save_correction_log(data, "", result, "", starttime=datetime.now(), endtime=datetime.now(), step_num=4, prompt_tokens=0, completion_tokens=0)
        except Exception as ee:
            logger.error(f"{self.log_prefix}get_score_count error: {ee}")
        
        return line_wrong, format_wrong, logical_wrong, student_score, score_conclusion, key_point_review, comment_conclusion, keyPoint_lost
    
    @staticmethod
    def remove_same(line_result):
        # 处理重复情况
        def is_subset(answer_i, answer_j):
            return answer_i in answer_j
        
        def merge_scores(score_i, score_j):
            return str(score_i + score_j) + '分'
        
        def merge_key_point_ids(key_point_ids_i, key_point_ids_j):
            """合并两个 keyPointIds 列表，去重并保持顺序"""
            seen = set()
            result = []

            for key_point_id in (key_point_ids_i or []) + (key_point_ids_j or []):
                if key_point_id not in seen:
                    seen.add(key_point_id)
                    result.append(key_point_id)

            return result
        
        def remove_internal_duplicates(answer):
            parts = separate_text_by_punctuation(answer)
            parts = [part.strip() for part in parts if part.strip()]
            unique_parts = []
            for part in parts:
                if part not in unique_parts:    
                    unique_parts.append(part)
            return ','.join(unique_parts)
        
        for i in range(len(line_result)):
            line_result[i]['keyPointIds'] = [line_result[i]["keyPointId"]]

        i = 0
        while i < len(line_result):
            if line_result[i]['回答要点分类'] == '遗漏':
                i += 1
                continue
            j = i + 1
            while j < len(line_result):
                if line_result[j]['回答要点分类'] == '遗漏':
                    j += 1
                    continue
                answer_i = line_result[i]['回答要点']
                answer_j = line_result[j]['回答要点']
                score_i = float(get_clear_score(line_result[i]['要点得分']))
                score_j = float(get_clear_score(line_result[j]['要点得分']))

                # 合并完全一致或子集关系的回答要点,保留回答要点多的
                if is_subset(answer_j, answer_i):
                    line_result[i]['要点得分'] = merge_scores(score_i, score_j)
                    # 合并keyPointIds
                    line_result[i]['keyPointIds'] = merge_key_point_ids(
                        line_result[i]['keyPointIds'], 
                        line_result[j]['keyPointIds']
                    )
                    del line_result[j]
                    continue
                elif is_subset(answer_i, answer_j):
                    line_result[i]['要点得分'] = merge_scores(score_j, score_i)
                    line_result[i]['回答要点'] = answer_j
                    # 合并keyPointIds
                    line_result[i]['keyPointIds'] = merge_key_point_ids(
                        line_result[i]['keyPointIds'], 
                        line_result[j]['keyPointIds']
                    )
                    del line_result[j]
                    continue

                # 处理部分重合
                answer_i_slices = separate_text_by_punctuation(answer_i)
                answer_j_slices = separate_text_by_punctuation(answer_j)
                answer_j_final_slices = []
                for answer_j_slice in answer_j_slices:
                    if answer_j_slice not in answer_i_slices:
                        answer_j_final_slices.append(answer_j_slice)
                line_result[j]['回答要点'] = ",".join(answer_j_final_slices)
                j += 1

            # 去掉内部重复的子集
            line_result[i]['回答要点'] = remove_internal_duplicates(
                line_result[i]['回答要点'])
            i += 1

        return line_result
    
    def add_gpt_result(self, data, line_result, need_gpt_list, key_point_line_result):
        for id, item in enumerate(need_gpt_list):
            try:
                # 获取要点id
                keyPoint_num = int(list(item.keys())[0].split("-")[0].replace("要点", '')) - 1
                match_answer = key_point_line_result[0][id]['匹配回答内容'].strip()
                if match_answer in ["无", ""]:
                    continue

                matched, not_matched = slice_and_match([match_answer], data['userAnswer'])
                # 如果不为空，那么说明是有匹配内容
                if matched != []:
                    line_result[keyPoint_num]['回答要点'] += "," + match_answer if line_result[keyPoint_num]['回答要点'] != "" else match_answer

            except Exception as ee:
                logger.error(f"{self.log_prefix}add_gpt_result error = {ee}")

        # 处理遗漏要点
        for id in range(len(line_result)):
            answer_point = line_result[id].get('回答要点')
            if not isinstance(answer_point, str) or answer_point.strip() in ["无", ""]:
                line_result[id]['回答要点分类'] = "遗漏"
                line_result[id]['回答要点'] = "无"
                line_result[id]['要点得分'] = 0

        return line_result
    
    def get_start_end_index(self, data, line_result, format_result):
        student_answer = data['userAnswer']
        
        # 处理要点划线
        group, under_lines, all_ranges = self._process_line_result_for_index(student_answer, line_result)
        
        # 处理格式点评划线
        group, under_lines, all_ranges = self._process_format_result_for_index(student_answer, format_result, line_result, group, under_lines, all_ranges)
                
        result = {
            "correctedContent": student_answer,
            "underLines": under_lines,
            "group": group
        }
        return result
    
    def _process_line_result_for_index(self, student_answer, line_result):
        """处理要点划线结果，提取索引位置"""
        group, under_lines, all_ranges = [], [], []
        for idx, item in enumerate(line_result):
            answer_point = item['回答要点']
            score = float(get_clear_score(item['要点得分']))
            if answer_point in ['无', ''] or score <= 0:
                continue

            start_end_list = self._get_text_positions(student_answer, answer_point)
            if not start_end_list:
                logger.warning(f"{self.log_prefix}start_end_list is empty, answer_point={answer_point}")
                continue

            start_end_list = sorted(start_end_list, key=lambda x: x['end'])

            # 获取keyPointIds，如果没有则默认为空列表
            key_point_ids = item.get('keyPointIds', [])

            group.append({
                "id": f"Array{idx + 1}",
                "score": score,
                "data": start_end_list,
                "keyPointIds": key_point_ids
            })

            under_lines.extend([{'start': d['start'], 'end': d['end'], 'score': score if i == len(
                start_end_list) - 1 else 0, 'keyPointIds': key_point_ids} for i, d in enumerate(start_end_list)])
            all_ranges.extend([(d['start'], d['end']) for d in start_end_list])
        
        return group, under_lines, all_ranges
    
    def _process_format_result_for_index(self, student_answer, format_result, line_result, group, under_lines, all_ranges):
        """处理格式点评划线结果，提取索引位置"""
        for idx, key in enumerate([key for key in format_result.keys() if "得分" not in key]):
            current_idx = idx + len(line_result)
            format_point = format_result[key]
            score = float(format_result[f"{key}得分"])
            if format_point in ['无', ''] or score <= 0:
                continue
                
            start_end_list = self._get_text_positions(student_answer, format_point)
            if not start_end_list:
                logger.warning(f"{self.log_prefix}start_end_list is empty, format_point={format_point}")
                continue
            
            start_end_list = sorted(start_end_list, key=lambda x: x['end'])

            # 应用文没有要点ID
            key_point_ids = []

            group.append({
                "id": f"Array{current_idx + 1}",
                "score": score,
                "data": start_end_list,
                "keyPointIds": key_point_ids
            })

            under_lines.extend([{'start': d['start'], 'end': d['end'], 'score': score if i == len(
                start_end_list) - 1 else 0, 'keyPointIds': key_point_ids} for i, d in enumerate(start_end_list)])
            all_ranges.extend([(d['start'], d['end']) for d in start_end_list])
        
        return group, under_lines, all_ranges
    
    def _get_text_positions(self, text, point_text):
        """查找文本中所有匹配子串的位置"""
        result = []
        
        # 根据标点符号切分要点内容
        point_parts = separate_text_by_punctuation(point_text)
        point_parts = [part.strip() for part in point_parts if part.strip()]
        for point_part in point_parts:
            start = text.find(point_part)
            if start == -1:
                logger.warning(f"{self.log_prefix}point_part={point_part} not in user_answer")
                continue

            end = start + len(point_part)
            result.append({
                "start": start,
                "end": end,
                'text': text[start:end]
            })
        
        return result

    async def process_key_point(self, model_cls, data, line_result, tokens):
        #  根据回答要点进行要点点评分数矫正
        line_result = await score.correct_score(data, line_result)
        need_gpt_list = self.check_in_answer(data, line_result)
        # 到这一步，可以确定工程侧给到要点分数以及模型给到分数一定是正常的（不会出现分数<0 不会出现分数不为0.5的倍数 不会出现模型得分大于工程侧分数）
        if need_gpt_list:
            key_point_prompt = await get_keyPoint_prompt(need_gpt_list, data)
            key_point_line_result = await asyncio.gather(model_cls.round_parse(data, tokens, key_point_prompt, 8))
            line_result = self.add_gpt_result(data, line_result, need_gpt_list, key_point_line_result)
        return line_result
    
    async def process_format(self, data, format_result):
        #  根据回答要点进行要点点评分数矫正
        format_result = await score.correct_format_score(data, format_result)
        return format_result
    
    @staticmethod
    async def process_format_and_key_points(line_result, format_result):
        """处理line_result和format_result，移除重复内容
        
        将line_result中的'回答要点'按标点符号切片，
        检查每个切片是否出现在format_result的非得分key中的切片里，
        如果存在则从line_result的'回答要点'中移除对应切片。
        
        Args:
            line_result: 批改结果列表
            format_result: 格式结果字典
            
        Returns:
            处理后的line_result
        """
        # 获取format_result中所有非得分key的值并切片
        format_slices = []
        for key, value in format_result.items():
            if not key.endswith('得分') and value not in ["无", ""]:
                format_slices.extend([s.strip() for s in separate_text_by_punctuation(value) if s.strip()])
                
        # 处理line_result中的每一个要点
        for id in range(len(line_result)):
            answer_point = line_result[id].get('回答要点')
            if isinstance(answer_point, str) and answer_point.strip() not in ["无", ""]:
                # 将回答要点切片
                answer_slices = [s.strip() for s in separate_text_by_punctuation(answer_point) if s.strip()]
                
                # 过滤掉已在format_result中存在的切片
                filtered_slices = [s for s in answer_slices if s not in format_slices]
                
                # 更新回答要点
                if filtered_slices:
                    line_result[id]['回答要点'] = ",".join(filtered_slices)
                else:
                    line_result[id]['回答要点分类'] = "遗漏"
                    line_result[id]['回答要点'] = "无"
                    line_result[id]['要点得分'] = 0

        return line_result
        
    async def get_final_score_and_review(self, data, line_result, logical_result, format_result):
        # 获得要点划线批改、逻辑条理后的扣分情况，生成点评
        line_wrong, format_wrong, logical_wrong, student_score, score_conclusion, key_point_review, comment_conclusion, keyPoint_lost = await self.get_score_count(data, line_result, logical_result, format_result)

        # 进行去重、合并、定位的操作
        line_result = self.remove_same(line_result)
        start_end_index_result = self.get_start_end_index(data, line_result, format_result)

        return line_wrong, format_wrong, logical_wrong, student_score, score_conclusion, key_point_review, comment_conclusion, line_result, start_end_index_result, keyPoint_lost


def transform_format_requirement(format_requirement):
    """
    format_requirement 转换成格式得分
    """
    format_points = {
        "标题": 0,
        "称谓": 0,
        "发文主体": 0,
        "发文日期": 0
    }
    if not isinstance(format_requirement, dict) or not format_requirement:
        return format_points, "无"
    
    result = {}
    title_score = get_format_score(format_requirement.get("标题"))
    if title_score:
        result["标题"] = f"{title_score}分"
        format_points["标题"] = title_score
    appellation_score = get_format_score(format_requirement.get("称谓"))
    if appellation_score:
        result["称谓"] = f"{appellation_score}分"
        format_points["称谓"] = appellation_score
        
    sign_result = {}
    publish_subject_score = get_format_score(format_requirement.get("发文机关"))
    if publish_subject_score:
        sign_result["发文主体"] = f"{publish_subject_score}分"
        format_points["发文主体"] = publish_subject_score
    publish_date_score = get_format_score(format_requirement.get("发文日期"))
    if publish_date_score:
        sign_result["发文日期"] = f"{publish_date_score}分"
        format_points["发文日期"] = publish_date_score
    if sign_result:
        result["落款"] = sign_result

    return format_points, result if result else '无'


async def get_essential_data(data, log_prefix=""):
    # 获取折算比例，默认为1
    conversion_ratio = data.get('questionConversionRatio', 1)
    has_conversion = conversion_ratio != 1
    
    # 获取格式要求和格式得分
    format_points, format_requirement = transform_format_requirement(data.get('formatRequirement'))
    convert_format_points, _ = transform_format_requirement(data.get('convertFormatRequirement'))

    # 获取逻辑条理扣分
    logical_total_score = data['questionDeductionPoints'][0]['value'][1]
    convert_logical_total_score = data['questionDeductionPoints'][0]['convertValue'][1] if has_conversion else logical_total_score

    keyPoint = {
        "questionKeyPoints": data['questionKeyPoints'],
        "answer": remove_tags_bs4(data['answer']),
        "answerRequirement": remove_tags_bs4(data['answerRequirement']),
        "logicalTotalScore": logical_total_score,
        "convertLogicalTotalScore": convert_logical_total_score,
        "score": data['score'],
        "convertScore": data['convertScore'] if has_conversion else data['score'],  # 折算后的总分
        "articleType": data['articleType'] if isinstance(data.get('articleType'), str) else '',
        "formatPoints": format_points,
        "formatRequirement": format_requirement,
        "convertFormatPoints": convert_format_points,  # 折算后的格式要求
        "conversionRatio": conversion_ratio,  # 添加折算比例
        "hasConversion": has_conversion,  # 是否折算
    }
    data['keyPoint'] = keyPoint
    data['userAnswer'] = convert_chinese_punctuation_to_english(data['userAnswer'])

    # 获取要点划线批改Prompt和逻辑条理扣分Prompt
    line_prompt, logical_prompt = await asyncio.gather(
        get_line_prompt(data),
        get_logical_prompt(data)
    )
    if not line_prompt:
        raise Exception("要点划线批改模块获取prompt模板失败")
    if not logical_prompt:
        raise Exception("逻辑条理扣分模块获取prompt模板失败")

    #  根据输入内容，根据字符长度判断，进而选择第一步模型
    tokens = await get_tokens(line_prompt)
    if not tokens:
        raise Exception("获取token数量失败")
    logger.info(f"{log_prefix}第一轮prompt，tokens获取成功，开始解析")
    return data, line_prompt, logical_prompt, tokens


async def run_essay_correction(data, log_prefix=""):
    """
    概括归纳题&解决问题&综合分析（要素分析）&综合分析（比较分析、词句理解、观点/现象评价）&应用文写作
    """
    model_cls = ModelClient(log_prefix)
    key_point_cls = KeyPointProcessor(log_prefix)

    # 获取必要数据
    data, line_prompt, logical_prompt, tokens = await get_essential_data(data)

    # 获取要点划线和逻辑条理的结果
    line_result, logical_result = await model_cls.get_line_logical_results(model_cls, data, tokens, line_prompt, logical_prompt)

    # 应用文需要单独处理
    if data["questionType"] == 4:
        format_result = line_result["格式得分"] if isinstance(line_result.get("格式得分"), dict) else {}
        format_result = await key_point_cls.process_format(data, format_result)
        format_review = line_result["格式点评"].strip() if isinstance(line_result.get("格式点评"), str) else ""
        line_result = line_result["回答要点批改结果"]
        logical_key = "逻辑层次点评"
    else:
        format_result, format_review = {}, ""
        logical_key = "逻辑条理点评"

    # 处理回答要点
    line_result = await key_point_cls.process_key_point(model_cls, data, line_result, tokens)

    # 回答要点去掉与格式点评重复的要点
    line_result = await key_point_cls.process_format_and_key_points(line_result, format_result)

    # 到这儿后，已经能确定，要点划线的内容是一定出现在学院作答中。进行分数的计算、点评的计算、点评的获取等。
    # 为什么不先去重的原因，是因为，假设要点1、要点2均为准确，那么应判准确个数+2 如果合并了，那么准确个数只能+1.会导致计算有误。
    # 1、获得要点划线批改、逻辑条理后将 的扣分情况拿到 2、根据扣分情况，需要将第五点内容要点点评生成拿到 3、获取扣分情况，获取点评
    line_wrong, format_wrong, logical_wrong, student_score, score_conclusion, key_point_review, comment_conclusion, line_result, start_end_index_result, keyPoint_lost = await key_point_cls.get_final_score_and_review(data, line_result, logical_result, format_result)

    # 获取最终点评结果
    last_result = await model_cls.get_final_prompt_and_result(model_cls, data, tokens, format_review, logical_result, key_point_review, comment_conclusion)

    to_java_result = [
        {
            "itemKey": "answerInfo",
            "itemValue": {
                "answerId": data['answerId'],  # 作答id
                "questionId": data['questionId'],  # 题目id
                "userAnswer": data['userAnswer'],  # 用户答案
                "version": data['version'],  # 原样返回
                "correctId": data['correctId'],  # 原样返回
                "questionConversionRatio": data.get('questionConversionRatio'), # 折算比例 原样返回
                "correctTime": str(datetime.now()),  # 批改时间字段
                "status": 1,  # 1成功 2失败 失败只返回 answerInfo 这块内容
                "reason": "",  # 失败原因，成功时为空
                "errorCode": None  # 错误码, 成功时为None
            },  # 作答信息
            "style": "object"
        },
        {
            "itemKey": "studentScore",
            "itemValue": student_score,  # 学生得分
            "style": "text"
        },
        {
            "itemKey": "scoreConclusion",
            "itemValue": score_conclusion,  # 批改扣分
            "style": "text"
        },
        {
            "itemKey": "topicReviewCognition",
            "itemValue": last_result[0]['审题认知点评'].strip(),  # 审题认知
            "style": "text"
        },
        {
            "itemKey": "logicCommentary",
            # "条理清晰。分类合理。回答整体以序号形式分点呈现，形式上有条理。且各项措施内容分类明确，从品牌建设、市场经营、生态修复、规模经营、管理服务等不同方面对发展山核桃产业的措施进行罗列，同类内容合并，异类内容分别列出，逻辑清晰合理",  # 逻辑条理点评
            "itemValue": logical_result[logical_key].strip(),
            "style": "text"
        },
        {
            "itemKey": "languageCommentary",
            "itemValue": last_result[0]['语言表达点评'].strip(),  # 语言表达点评
            "style": "text"
        },
        {
            "itemKey": "keyCommentary",
            "itemValue": key_point_review.strip(),  # 内容要点点评
            "style": "text"
        },
    ]

    # 应用写作题型需要单独返回格式点评
    if data["questionType"] == 4:
        to_java_result.append({
            "itemKey": "formatCommentary",
            "itemValue": format_review, # 格式点评（应用写作题型）
            "style": "text"
        })
    to_java_result.extend([
        {
            "itemKey": "overallCommentary",  # 整体点评
            "itemValue": last_result[0]['整体点评'].strip(),
            "style": "text"
        },
        {
            "itemKey": "groupScore",  # 定位详情
            "itemValue": start_end_index_result,
            "style": "object"
        },
        {
            "itemKey": "keyPointIds",  # 遗漏要点ids
            "itemValue": keyPoint_lost,
            "style": "object"
        }
    ])
    risk_content = await risk_assessment(str(last_result))
    if risk_content:
        raise Exception(f"触发敏感词,result is {last_result}")
    
    return to_java_result


def transform_bonus_points(question_bonus_points, has_conversion=False):
    """
    处理加分项（行文文采好、卷面美观）
    [{"bonusPointId":8,"bonusPointContent":"行文文采好","value":[-1.0,1.0],"upperConditionLimit":-1}]
    """
    bonus_points = {
        "行文文采好": 0,
        "卷面美观": 0
    }
    bonus_points_scope = {
        "行文文采好": "0分",
        "卷面美观": "0分"
    }
    for point in question_bonus_points:
        value = point['value'][1] if not has_conversion else point['convertValue'][1]
        bonus_points[point['bonusPointContent']] = value
        bonus_points_scope[point['bonusPointContent']] = f"0-{value}分" if value > 0 else f"0分"

    return bonus_points, bonus_points_scope


def transform_deduction_points(question_deduction_points, has_conversion=False):
    """
    处理扣分项（字迹无法辨认、标题缺失、标题不符、标题文章不符、错别字、超出字数范围）
    {"deductionPointId":4,"deductionPointContent":"标题不符","value":[-1.0,1.0],"upperConditionLimit":-1}
    """
    deduction_points = {
        "超出字数范围": 0,
        "标题缺失": 0,
        "标题不符": 0,
        "标题文章不符": 0,
    }
    deduction_points_scope = {
        "超出字数范围": "0分",
        "标题缺失": "0分",
        "标题不符": "0分",
        "标题文章不符": "0分",
    }
    for point in question_deduction_points:
        value = point['value'][1] if not has_conversion else point['convertValue'][1]
        deduction_points[point['deductionPointContent']] = value
        deduction_points_scope[point['deductionPointContent']] = f"0-{value}分" if value > 0 else f"0分"

    return deduction_points, deduction_points_scope


def get_article_level_setting(settings, has_conversion=False):
    """
    处理文章内容等级, 返回文章内容等级映射和文章内容等级key, 查询时从四等级往上找，如果一等级查询不到，则返回一等级
    {
        "articleLevelSetting": {
            "1": {"levelName": "一类文", "wordsLower": 801, "wordsUpper": 1200, "scoreLower": 32.0, "scoreUpper": 40.0, "convertScoreLower": 32.0, "convertScoreUpper": 40.0},
            "2": {"levelName": "二类文", "wordsLower": 401, "wordsUpper": 800, "scoreLower": 24.0, "scoreUpper": 32.0, "convertScoreLower": 24.0, "convertScoreUpper": 32.0},
            "3": {"levelName": "三类文", "wordsLower": 201, "wordsUpper": 400, "scoreLower": 12.0, "scoreUpper": 24.0, "convertScoreLower": 12.0, "convertScoreUpper": 24.0},
            "4": {"levelName": "四类文", "wordsLower": 0, "wordsUpper": 200, "scoreLower": 0.0, "scoreUpper": 12.0, "convertScoreLower": 0.0, "convertScoreUpper": 12.0}
        },
        "articleLevelKey": ["4", "3", "2", "1"]
    }
    """
    type_to_chinese = {
        "1": "一类文",
        "2": "二类文",
        "3": "三类文",
        "4": "四类文"
    }
    article_key = ["4", "3", "2", "1"]
    article_level_map = {
        str(item["type"]): {
            "levelName": type_to_chinese[str(item["type"])],
            "wordsLower": item["wordsLower"],
            "wordsUpper": item["wordsUpper"],
            "scoreLower": item["scoreLower"],
            "scoreUpper": item["scoreUpper"],
            "convertScoreLower": item["convertScoreLower"] if has_conversion else item["scoreLower"],
            "convertScoreUpper": item["convertScoreUpper"] if has_conversion else item["scoreUpper"]
        }
        for item in settings
    }

    # 文章内容等级映射
    article_level_setting = {
        "articleLevelSetting": article_level_map,
        "articleLevelKey": article_key
    }
    return article_level_setting


def get_article_level_by_words(word_count, article_level_dict):
    """
    根据字数从低等级向高等级查找所属等级
    """
    article_level = []
    final_word_count = word_count + 75
    setting = article_level_dict["articleLevelSetting"]
    level_keys = article_level_dict["articleLevelKey"]
    for level in level_keys:
        if final_word_count >= setting[level]["wordsLower"]:
            article_level.append(setting[level]["levelName"])
        else:
            break
    article_level.reverse()
    article_level = article_level if article_level else ["四类文"]
    return "、".join(article_level)


def get_article_level_by_score(score, article_level_dict, has_conversion=False):
    """
    根据分数从低等级向高等级查找所属等级
    """
    setting = article_level_dict["articleLevelSetting"]
    level_keys = article_level_dict["articleLevelKey"]
    key = "scoreUpper" if not has_conversion else "convertScoreUpper"
    for level in level_keys:
        if score <= setting[level][key]:
            return setting[level]["levelName"]
    return "一类文"


def get_exceeding_word_deduction_and_length_status(data, has_conversion=False):
    """
    获取超字数扣分数和作答字数状态
    """
    exceeding_info = data["exceedingWord"]
    if not exceeding_info:
        if data["answerLength"] + 75 < data["lowerWordLimit"]:
            return 0, "作答字数未达最低字数要求"
        else:
            return 0, "作答字数达到最低字数要求"
        
    # 获取折算后的超字数扣分数
    exceeding_word_deduction = exceeding_info["deductionScore"] if not has_conversion else exceeding_info["convertDeductionScore"]
    return exceeding_word_deduction, "作答字数超过字数要求"


async def get_article_writing_essential_data(data):
    """
    获取文章写作必要数据
    """
    # 获取折算比例，默认为1
    conversion_ratio = data.get('questionConversionRatio', 1)
    has_conversion = conversion_ratio != 1
    
    # 获取加分项和加分项范围
    bonus_points, bonus_points_scope = transform_bonus_points(data['questionBonusPoints'])
    convert_bonus_points, _ = transform_bonus_points(data['questionBonusPoints'], has_conversion=has_conversion)
    
    # 获取扣分项和扣分项范围
    deduction_points, deduction_points_scope = transform_deduction_points(data['questionDeductionPoints'])
    convert_deduction_points, _ = transform_deduction_points(data['questionDeductionPoints'], has_conversion=has_conversion)
    
    # 获取文章内容等级映射和文章内容等级key
    article_level_setting = get_article_level_setting(data['gearShiftSettings'], has_conversion)
    article_level = get_article_level_by_words(data["answerLength"], article_level_setting)
    exceeding_word_deduction, answer_length_status = get_exceeding_word_deduction_and_length_status(data, has_conversion)
    keyPoint = {
        "questionKeyPoints": data['questionKeyPoints'],
        "answerRequirement": remove_tags_bs4(data['answerRequirement']),
        "score": data['score'],
        "convertScore": data['convertScore'] if has_conversion else data['score'],  # 折算后的总分
        "bonusPoints": bonus_points,
        "bonusPointsScope": bonus_points_scope,
        "deductionPoints": deduction_points,
        "deductionPointsScope": deduction_points_scope,
        "articleLevelSetting": article_level_setting,
        "articleLevel": article_level,
        "exceedingWordDeduction": exceeding_word_deduction,
        "answerLengthStatus": answer_length_status,
        "convertBonusPoints": convert_bonus_points,  # 折算后的加分项
        "convertDeductionPoints": convert_deduction_points,  # 折算后的扣分项
        "conversionRatio": conversion_ratio,  # 添加折算比例
        "hasConversion": has_conversion,  # 是否折算
    }
    data['keyPoint'] = keyPoint
    return data


class ArticleWritingProcessor:
    """文章写作批改处理器"""
    @classmethod
    async def correct_and_merge_images(cls, data, log_prefix=""):
        """
        矫正和合并图片 - 使用API接口
        """
        try:
            object_id = data["answerId"]
            images = [
                {
                    "url": image["url"],
                    "id": image.get("id", 0),
                    "sort": image.get("sort", i + 1)
                }
                for i, image in enumerate(data["images"])
            ]
            corrected_images, final_url = await ImageService.correct_and_merge(object_id, images, log_prefix)
            return corrected_images, final_url
        except Exception as e:
            logger.error(f"{log_prefix}矫正和合并图片失败: {e}", exc_info=True)
            return [], ""
    
    @staticmethod
    async def calculate_deductions(data, grading_result):
        """计算扣分项"""
        has_conversion = data['keyPoint']['hasConversion']
        conversion_ratio = data['keyPoint']['conversionRatio']

        exceeding_word_deduction = data['keyPoint']['exceedingWordDeduction']
        title_missing_deduction = get_clear_score(grading_result.get("标题缺失扣分", 0))
        title_argument_mismatch_deduction = get_clear_score(grading_result.get("标题不符中心论点扣分", 0))
        title_content_mismatch_deduction = get_clear_score(grading_result.get("标题与正文不符扣分", 0))
        
        # 确保扣分项是0.5的倍数，且不超过最高分上限
        if title_missing_deduction > 0:
            title_missing_deduction = data['keyPoint']['deductionPoints']['标题缺失'] if not has_conversion else data['keyPoint']['convertDeductionPoints']['标题缺失']
        else:
            title_missing_deduction = 0
        
        title_argument_mismatch_deduction = score.adjust_to_half_multiple(
            title_argument_mismatch_deduction, 
            data['keyPoint']['deductionPoints']['标题不符']
        )
        
        title_content_mismatch_deduction = score.adjust_to_half_multiple(
            title_content_mismatch_deduction, 
            data['keyPoint']['deductionPoints']['标题文章不符']
        )

        if has_conversion:
            if title_argument_mismatch_deduction == data['keyPoint']['deductionPoints']['标题不符']:
                title_argument_mismatch_deduction = data['keyPoint']['convertDeductionPoints']['标题不符']
            else:
                title_argument_mismatch_deduction = score.convert_score(title_argument_mismatch_deduction, conversion_ratio, data['keyPoint']['convertDeductionPoints']['标题不符'])

            if title_content_mismatch_deduction == data['keyPoint']['deductionPoints']['标题文章不符']:
                title_content_mismatch_deduction = data['keyPoint']['convertDeductionPoints']['标题文章不符']
            else:
                title_content_mismatch_deduction = score.convert_score(title_content_mismatch_deduction, conversion_ratio, data['keyPoint']['convertDeductionPoints']['标题文章不符'])

        return exceeding_word_deduction, title_missing_deduction, title_argument_mismatch_deduction, title_content_mismatch_deduction

    @staticmethod
    async def calculate_bonuses(data, appearance_result, grading_result):
        """计算加分项"""
        has_conversion = data['keyPoint']['hasConversion']
        conversion_ratio = data['keyPoint']['conversionRatio']

        writing_style_bonus = get_clear_score(grading_result.get("文采加分", 0))
        appearance_bonus = 0
        if appearance_result.get("卷面美观度") == "优":
            appearance_bonus = get_clear_score(data['keyPoint']['bonusPoints']['卷面美观'])
        
        # 调整加分项
        writing_style_bonus = score.adjust_to_half_multiple(
            writing_style_bonus, 
            data['keyPoint']['bonusPoints']['行文文采好']
        )
        
        appearance_bonus = score.adjust_to_half_multiple(
            appearance_bonus, 
            data['keyPoint']['bonusPoints']['卷面美观']
        )
        
        if has_conversion:
            if writing_style_bonus == data['keyPoint']['bonusPoints']['行文文采好']:
                writing_style_bonus = data['keyPoint']['convertBonusPoints']['行文文采好']
            else:
                writing_style_bonus = score.convert_score(writing_style_bonus, conversion_ratio, data['keyPoint']['convertBonusPoints']['行文文采好'])

            if appearance_bonus == data['keyPoint']['bonusPoints']['卷面美观']:
                appearance_bonus = data['keyPoint']['convertBonusPoints']['卷面美观']
            else:
                appearance_bonus = score.convert_score(appearance_bonus, conversion_ratio, data['keyPoint']['convertBonusPoints']['卷面美观'])
        
        return writing_style_bonus, appearance_bonus

    @staticmethod
    def generate_deduction_conclusions(exceeding_word_deduction, title_missing_deduction, title_argument_mismatch_deduction, title_content_mismatch_deduction):
        """
        生成扣分说明文本
        
        Args:
            exceeding_word_deduction: 超字数扣分
            title_missing_deduction: 标题缺失扣分
            title_argument_mismatch_deduction: 标题不符中心论点扣分
            title_content_mismatch_deduction: 标题与正文不符扣分
            
        Returns:
            tuple: (工程侧扣分说明文本, 模型侧扣分说明文本)
        """
        # 返回工程侧的扣分说明文本
        deduction_items = []
        deduction_conclusion = ""
        # 传入给模型的扣分说明文本
        deduction_items_for_model = []
        deduction_conclusion_for_model = ""
        
        if exceeding_word_deduction > 0:
            deduction_items.append(f'超出字数范围扣<span style="color: red;">{exceeding_word_deduction}</span>分')
            deduction_items_for_model.append(f"超字数扣分：{exceeding_word_deduction}分")
        if title_missing_deduction > 0:
            deduction_items.append(f'标题缺失扣<span style="color: red;">{title_missing_deduction}</span>分')
            deduction_items_for_model.append(f"标题缺失扣分：{title_missing_deduction}分")
        if title_argument_mismatch_deduction > 0:
            deduction_items.append(f'标题不符扣<span style="color: red;">{title_argument_mismatch_deduction}</span>分')
            deduction_items_for_model.append(f"标题不符中心论点扣分：{title_argument_mismatch_deduction}分")
        if title_content_mismatch_deduction > 0:
            deduction_items.append(f'标题文章不符扣<span style="color: red;">{title_content_mismatch_deduction}</span>分')
            deduction_items_for_model.append(f"标题与正文不符扣分：{title_content_mismatch_deduction}分")
        
        if deduction_items:
            deduction_conclusion = '，'.join(deduction_items) + '。'
            deduction_conclusion_for_model = '，'.join(deduction_items_for_model) + '。'
            
        return deduction_conclusion, deduction_conclusion_for_model

    @staticmethod
    def generate_bonus_conclusions(writing_style_bonus, appearance_bonus):
        """
        生成加分说明文本
        
        Args:
            writing_style_bonus: 文采加分
            appearance_bonus: 卷面美观加分
            
        Returns:
            tuple: (工程侧加分说明文本, 模型侧加分说明文本)
        """
        # 返回工程侧的加分说明文本
        bonus_items = []
        bonus_conclusion = ""
        # 传入给模型的加分说明文本
        bonus_items_for_model = []
        bonus_conclusion_for_model = ""
        
        if writing_style_bonus > 0:
            bonus_items.append(f'行文文采好加<span style="color: red;">{writing_style_bonus}</span>分')
            bonus_items_for_model.append(f"文采加分：{writing_style_bonus}分")
        if appearance_bonus > 0:
            bonus_items.append(f'卷面美观加<span style="color: red;">{appearance_bonus}</span>分')
            bonus_items_for_model.append(f"卷面美观加分：{appearance_bonus}分")

        if bonus_items:
            bonus_conclusion = '，'.join(bonus_items) + '。'
            bonus_conclusion_for_model = '，'.join(bonus_items_for_model) + '。'
            
        return bonus_conclusion, bonus_conclusion_for_model
    
    @classmethod
    async def get_article_writing_score_count(cls, data, appearance_result, grading_result, log_prefix=""):
        """
        计算文章写作得分
        
        Args:
            data: 输入数据
            appearance_result: 卷面分析结果
            grading_result: 文章分级、标题扣分、文采加分结果
            
        Returns:
            tuple: (student_score, score_conclusion, bonus_conclusion, final_article_level)
        """
        try:
            # 获取是否折算
            has_conversion = data['keyPoint']['hasConversion']
            # 工程侧给到总分
            total_score = data['keyPoint']['score'] if not has_conversion else data['keyPoint']['convertScore']

            # 获取文章内容等级对应的分数范围
            article_level = grading_result["内容等级"]

            current_level_setting = data['keyPoint']['articleLevelSetting']['articleLevelSetting'][str(article_level)]
            # 基准分计算: 当前等级的分数范围的平均值，四舍五入取整
            if has_conversion:
                base_score = custom_round((current_level_setting["convertScoreLower"] + current_level_setting["convertScoreUpper"]) / 2, 0)
            else:
                base_score = custom_round((current_level_setting["scoreLower"] + current_level_setting["scoreUpper"]) / 2, 0)
            
            # 计算扣分项
            exceeding_word_deduction, title_missing_deduction, title_argument_mismatch_deduction, title_content_mismatch_deduction = await cls.calculate_deductions(data, grading_result)

            # 计算加分项
            writing_style_bonus, appearance_bonus = await cls.calculate_bonuses(data, appearance_result, grading_result)

            # 计算总扣分和总加分
            total_deduction = exceeding_word_deduction + title_missing_deduction + title_argument_mismatch_deduction + title_content_mismatch_deduction
            total_bonus = writing_style_bonus + appearance_bonus
            
            # 计算最终得分
            student_score = base_score - total_deduction + total_bonus

            student_score = custom_round(student_score, 1)
            
            # 确保最终得分不超过总分
            student_score = min(student_score, total_score)
            
            # 确保最终得分不小于0
            student_score = max(student_score, 0)
            
            # 确定最终的文章内容等级（如果得分超出当前等级范围，则调整等级）
            final_article_level = get_article_level_by_score(student_score, data['keyPoint']['articleLevelSetting'], has_conversion)
            
            # 扣分说明文本
            deduction_conclusion, deduction_conclusion_for_model = cls.generate_deduction_conclusions(exceeding_word_deduction, title_missing_deduction, title_argument_mismatch_deduction, title_content_mismatch_deduction)
            
            # 加分说明文本
            bonus_conclusion, bonus_conclusion_for_model = cls.generate_bonus_conclusions(writing_style_bonus, appearance_bonus)
            
            # 记录分析日志
            score_result = {
                "基准分": base_score,
                "超字数扣分": exceeding_word_deduction,
                "标题缺失扣分": title_missing_deduction,
                "标题不符中心论点扣分": title_argument_mismatch_deduction,
                "标题与正文不符扣分": title_content_mismatch_deduction,
                "文采加分": writing_style_bonus,
                "卷面美观加分": appearance_bonus,
                "学生得分": student_score,
                "初始文章内容等级": article_level,
                "最终文章内容等级": final_article_level,
                "扣分情况": deduction_conclusion,
                "加分情况": bonus_conclusion,
                "deduction_conclusion": deduction_conclusion_for_model,
                "bonus_conclusion": bonus_conclusion_for_model
            }
            
            AnalysisManager.save_correction_log(data, "", score_result, "", starttime=datetime.now(), endtime=datetime.now(), step_num=14, prompt_tokens=0, completion_tokens=0)
            return student_score, deduction_conclusion, bonus_conclusion, final_article_level, score_result
        except Exception as e:
            raise Exception(f"分数计算失败: {e}")
        
    @classmethod
    def split_article_writing_sentences(cls, text, title=""):
        """
        将文本分句，按照句号、问号、叹号、省略号进行分割
        特殊规则：
        - '.' 前后如果是字母或数字或.，则不分句（例如 www.example.com 或 3.14）
        - '。'后面紧跟着"或“，则把"或“加到上一个句子里
        - 如果存在标题，标题作为第一句，后面的内容按原逻辑分句
        """
        sentences_for_model = []
        sentences_result = []
        sentences = []
        group = []
        current_sentence = ""
        user_answer = ""
        
        title = title.strip()
        # 如果有标题且不为"无"或空字符串
        has_title = title not in ["", "无"]
        
        # 定义分句标点
        punctuations = ["。", "！", "？", "…", ".", "!", "?"]
        
        # 是否找到标题
        found_title = False
        # 如果有标题，需要找到标题在原文中的位置，并从标题后开始分句
        start_pos = 0
        if has_title:
            title_pos = text.find(title)
            if title_pos != -1:
                found_title = True
                start_pos = title_pos + len(title)
                sentences.append(text[:start_pos])
        
        # 从标题后开始分句
        i = start_pos
        text_len = len(text)
        while i < text_len:
            char = text[i]
            current_sentence += char
            
            # 判断是否为标点
            if char in punctuations:
                # 特殊处理 . 分句：前后是字母或数字时，不视为句末
                if char == ".":
                    prev_char = text[i - 1] if i > 0 else ""
                    next_char = text[i + 1] if i + 1 < len(text) else ""
                    # 如果前或后至少一个是字母、数字或点号，则跳过分句
                    if re.match(r"[a-zA-Z0-9.]", prev_char) or re.match(r"[a-zA-Z0-9.]", next_char):
                        i += 1
                        continue # 不分句
                # '。'后面紧跟着"或“，则把"或“加到上一个句子里
                elif char == "。":
                    next_char = text[i + 1] if i + 1 < len(text) else ""
                    if next_char in ['"', '”']:
                        current_sentence += next_char
                        i += 1

                # 满足分句条件
                if len(current_sentence.strip()) >= 4:
                    sentences.append(current_sentence)
                    current_sentence = ""
            i += 1
        
        # 处理最后一个句子
        if current_sentence.strip():
            if len(current_sentence.strip()) >= 4:
                sentences.append(current_sentence)
            elif sentences:
                # 如果最后一个句子长度小于4，合并到前一个句子
                sentences[-1] += current_sentence
            else:
                # 如果当前句子长度小于4，且没有句子，则直接添加到结果中
                sentences.append(current_sentence)
        
        index = 0
        for i, sentence in enumerate(sentences):
            sentence_idx = i + 1
            
            if found_title and i == 0:
                model_sentence = title
                sentence_type = "标题"
            else:
                model_sentence = sentence
                sentence_type = "正文"
            
            # 为模型准备的分句格式
            sentences_for_model.append({
                "分句ID": sentence_idx, 
                "分句内容": model_sentence,
                "分句类型": sentence_type
            })
            
            # 为结果准备的分句格式
            sentences_result.append({
                "id": sentence_idx, 
                "sentence": sentence, 
                "comment": "", 
                "polish": "",
                # "type": sentence_type
            })

            # 为用户显示添加序号
            indexed_sentence = f'<span style="color: #FFA23A;">({sentence_idx})</span>{sentence}'
            user_answer += indexed_sentence

            # 记录分句的起始和结束位置
            start_index = index
            end_index = start_index + len(f'({sentence_idx})') + len(sentence) - 1
            group.append({"start": start_index, "end": end_index})

            index = end_index + 1

        return sentences_for_model, sentences_result, user_answer, group
    
    @classmethod
    def merge_sentence_reviews(cls, sentences_result, sentence_review_result):
        """
        合并逐句点评结果
        """
        sentence_len = len(sentences_result)
        for review in sentence_review_result.get("分句点评结果", []):
            sentence_id = review.get("分句ID")
            if sentence_id and sentence_id <= sentence_len:
                sentences_result[sentence_id - 1]["comment"] = review["点评"].strip() if review["点评"].strip() != "无" else ""
                sentences_result[sentence_id - 1]["polish"] = review["润色示例"].strip() if review["润色示例"].strip() != "无" else ""
        
        return sentences_result


async def article_correction_without_senctence(model_cls, data, tokens, correct_status, log_prefix=""):
    """
    文章写作批改（不含逐句批改）
    """
    # 处理卷面分析
    image_ids = [] # 图片ID列表默认值
    appearance_result = { # 卷面分析结果默认值
        "卷面等级": "",
        "卷面美观度": "",
        "卷面美观度评级原因": "",
        "卷面点评": "",
    }
    risk_model_content = "" #  敏感词校验的默认值
    if data['images']:
        image_ids = [image['id'] for image in data['images']]
        corrected_images, final_url = await ArticleWritingProcessor.correct_and_merge_images(data, log_prefix)
        logger.info(f"{log_prefix}corrected_images = {corrected_images}, final_url = {final_url}")
        data['keyPoint']['correctedImages'] = corrected_images
        data['keyPoint']['finalUrl'] = final_url

        if not final_url:
            logger.error(f"{log_prefix}矫正和合并图片失败")
            correct_status['image_correct_status'] = 2 # 重新提交后，只传新图
        else:
            appearance_result_model = await model_cls.get_article_appearance_result(model_cls, data, final_url)
            if appearance_result_model is not None:
                appearance_result = appearance_result_model
                risk_model_content += json.dumps(appearance_result, ensure_ascii=False) + "\n"
                correct_status['written_correct_status'] = 1 # 卷面分析有结果
            else:
                correct_status['image_correct_status'] = 2 # 重新提交后，只传新图

    # 处理文章分级、标题扣分、文采加分
    grading_result = await model_cls.get_article_grading_result(model_cls, data)

    # 计算得分
    student_score, score_conclusion, bonus_conclusion, final_article_level, score_result = await ArticleWritingProcessor.get_article_writing_score_count(data, appearance_result, grading_result, log_prefix)

    # 处理语言表达点评、审题立意点评、论据点评、逻辑条理点评、写作建议（P1）
    article_review_result = await model_cls.get_article_overall_review_result(model_cls, data, tokens, appearance_result, score_result)
    writing_advice = [
        {
            "advice": article_review["建议"],
            "rate": int(article_review["紧急程度"]),
        }
        for article_review in article_review_result["写作建议"]
    ]

    risk_model_content += json.dumps(article_review_result, ensure_ascii=False)
    risk_content = await risk_assessment(risk_model_content)
    if risk_content:
        raise Exception(f"触发敏感词,result is {risk_model_content}")
    
    to_java_result = [
        {
            "itemKey": "answerInfo",
            "itemValue": {
                "answerId": data['answerId'],  # 作答id
                "questionId": data['questionId'],  # 题目id
                "userAnswer": data['userAnswer'],  # 用户答案
                "version": data['version'],  # 原样返回
                "correctId": data['correctId'],  # 原样返回
                "questionConversionRatio": data.get('questionConversionRatio'), # 折算比例 原样返回
                "correctTime": str(datetime.now()),  # 批改时间字段
                "status": 1,  # 1成功 2失败 失败只返回 answerInfo 这块内容
                "reason": "",  # 失败原因，成功时为空
                "errorCode": None,  # 错误码, 成功时为None
                "writtenCorrectStatus": correct_status['written_correct_status'],
                "imageCorrectStatus": correct_status['image_correct_status']
            },  # 作答信息
            "style": "object"
        },
        {
            "itemKey": "studentScore",
            "itemValue": student_score,  # 学生得分
            "style": "text"
        },
        {
            "itemKey": "scoreConclusion",
            "itemValue": score_conclusion,  # 批改扣分
            "style": "text"
        },
        {
            "itemKey": "bonusConclusion",
            "itemValue": bonus_conclusion,  # 批改加分
            "style": "text"
        },
        {
            "itemKey": "compositeRating",
            "itemValue": final_article_level,  # 文章内容等级
            "style": "text"
        },
        {
            "itemKey": "languageCommentary",
            "itemValue": article_review_result["语言表达点评"].strip(),  # 语言评价
            "style": "text"
        },
        {
            "itemKey": "writtenEvaluation",
            "itemValue": {
                "content": f"{appearance_result['卷面等级']}\n{appearance_result['卷面点评']}" if appearance_result["卷面等级"] else "",  # 评价，没有卷面分析结果是该字段为空串
                "imageIds": image_ids,  # 展示图片ID
            },
            "style": "object"
        },
        {
            "itemKey": "topicReviewCognition",
            "itemValue": article_review_result["审题立意点评"].strip(),  # 审题立意
            "style": "text"
        },
        {
            "itemKey": "argumentUse",
            "itemValue": article_review_result["论据点评"].strip(),  # 论据使用
            "style": "text"
        },
        {
            "itemKey": "logicCommentary",
            "itemValue": article_review_result["逻辑条理点评"].strip(),  # 逻辑条理
            "style": "text"
        },
        {
            "itemKey": "writingAdvice",
            "itemValue": writing_advice,  # 写作建议
            "style": "object"
        }
    ]
    return to_java_result

async def run_article_correction(data, correct_status, log_prefix=""):
    """
    文章写作
    """
    model_cls = ModelClient(log_prefix)

    # 获取必要数据
    data = await get_article_writing_essential_data(data)

    # 提取文章标题prompt
    title_extraction_prompt = await get_article_title_extraction_prompt(data)
    if title_extraction_prompt is None:
        raise Exception("获取文章标题提取prompt失败")
    
    # 调用模型获取标题
    title_extraction_result = await model_cls.round_parse(data, 0, title_extraction_prompt, step_num=11)
    if not title_extraction_result:
        raise Exception("获取文章标题失败")
    
    title = title_extraction_result["标题"].strip()
    
    # 逐句点评
    sentences_for_model, sentences_result, user_answer, group = ArticleWritingProcessor.split_article_writing_sentences(data['userAnswer'], title)
    data["keyPoint"]["userAnswer"] = user_answer
    data["keyPoint"]["group"] = group

    sentence_review_prompt = await get_article_sentence_review_prompt(data, sentences_for_model)
    if sentence_review_prompt is None:
        raise Exception("获取逐句点评prompt失败")
    # 根据输入内容，根据字符长度判断，进而选择模型
    tokens = await get_tokens(sentence_review_prompt)
    if not tokens:
        raise Exception("获取token数量失败")

    # 逐句点评和其他批改可以并行
    to_java_result, sentence_review_result = await asyncio.gather(
        article_correction_without_senctence(model_cls, data, tokens, correct_status, log_prefix),
        model_cls.get_sentence_review_result(model_cls, data, tokens, sentence_review_prompt)
    )
    final_sentences_result = ArticleWritingProcessor.merge_sentence_reviews(sentences_result, sentence_review_result)
    to_java_result.append({
            "itemKey": "sentenceCorrection",
            "itemValue": {
                "userAnswer": user_answer, # 学员作答原文 需要 AI 拼序号，如：<span style="color: #FFA23A;">(1)</span>
                "sentences": final_sentences_result, # 逐句点评结果
                "group": group,
            },  
            "style": "object"
        })
    
    risk_model_content = json.dumps(sentence_review_result, ensure_ascii=False)
    risk_content = await risk_assessment(risk_model_content)
    if risk_content:
        raise Exception(f"触发敏感词,result is {sentence_review_result}")
    
    return to_java_result


async def run(data, request=None):
    to_java_result = []
    start_time = datetime.now()
    
    correct_status = {
        "written_correct_status": 2,  # 卷面分析状态指的是：1有结果，2没结果 （默认为 2）
        "image_correct_status": 1   # 图片批改状态包括：1重新提交后，把所有图片都传给 AI 侧（新图+旧图），2重新提交后，只传新图（默认为 1）
    }

    try:
        # 校验数据结构，如果数据结构有误，则返回错误原因
        is_pass, wrong_text = check_correction_structure(data)
        if not is_pass:
            raise Exception(f"数据结构有误: {wrong_text}")
        
        log_prefix = f"answer_id={data['answerId']}, "
        logger.info(f"{log_prefix}start eassy correction, data={json.dumps(data, ensure_ascii=False)}")
        # 文章写作批改
        if data['questionType'] == 5:
            to_java_result = await run_article_correction(data, correct_status, log_prefix)
        # 其他题型
        else:
            to_java_result = await run_essay_correction(data, log_prefix)

    except Exception as e:
        logger.error(f"eassy correction run error = {e}, request_data = {data}", exc_info=True)
        error_message = str(e)
        error_code = ErrorClassifier.get_error_code(error_message, "correction")
        default_result = {
            "itemKey": "answerInfo",
            "itemValue": {
                "answerId": data.get('answerId'),  # 作答id
                "questionId": data.get('questionId'),  # 题目id
                "userAnswer": data.get('userAnswer'),  # 用户答案
                "version": data.get('version'),  # 原样返回
                "correctId": data.get('correctId'),  # 原样返回
                "questionConversionRatio": data.get('questionConversionRatio'), # 折算比例 原样返回
                "correctTime": str(datetime.now()),  # 批改时间字段
                "status": 2,  # 1成功 2失败 失败只返回 answerInfo 这块内容
                "reason": error_message,  # 失败原因
                "errorCode": error_code  # 错误码
            },  # 作答信息
            "style": "object"
        }
        # 文章写作添加卷面分析状态和图片批改状态
        if data.get('questionType') == 5:
            default_result['itemValue']['writtenCorrectStatus'] = correct_status['written_correct_status']
            default_result['itemValue']['imageCorrectStatus'] = correct_status['image_correct_status']
            
        to_java_result = [default_result]
    
    return to_java_result, datetime.now() - start_time  # 产品要求返回此次消耗时间
