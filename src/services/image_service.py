from utils.request import fetch
from utils.logger import logger
from config.settings import config


class ImageService:
    """图片服务类"""
    
    api_config = config["ImageAPI"]
    image_merge_url = str(api_config["image_merge_url"])
    project_id = int(api_config["project_id"])
    project_name = str(api_config["project_name"])
    
    @classmethod
    async def correct_and_merge(cls, object_id, images, log_prefix=""):
        """
        处理图片矫正合并
        
        Args:
            object_id: 对象ID
            images: 图片列表
            log_prefix: 日志前缀，可选
            
        Returns:
            tuple: (corrected_images, final_url)
        """
        # 构造请求参数
        request_data = {
            "project_id": cls.project_id,
            "project_name": cls.project_name,
            "object_id": object_id,
            "images": images
        }
        
        # 发送API请求
        result = await fetch(
            cls.image_merge_url,
            method='POST',
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if result and result.get("code") == 10000:
            # 成功
            api_data = result.get("data", {})
            corrected_images = api_data.get("images", [])
            final_url = api_data.get("final_url", "")
            return corrected_images, final_url
        else:
            # API返回错误或失败
            logger.error(f"{log_prefix}image correct and merge failed: {result}")
            return [], ""