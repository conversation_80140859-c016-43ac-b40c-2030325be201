# Kafka 服务设计文档

## 1. 概述

本文档旨在详细阐述 `src/services/kafka` 目录下 Kafka 服务的技术架构、核心组件、工作流程及使用方法。该服务为项目提供了稳定、高效、可扩展的异步消息处理能力。

### 1.1. 设计目标

- **高可用性**: 服务具备自动重连、故障恢复和健康检查机制，确保在 Broker 或网络不稳时能自我修复。
- **高性能**: 通过批量消费、批量生产、异步处理和 Gzip 压缩等策略，最大化消息吞吐量。
- **可靠性**: 采用手动提交 Offset、Redis 备份 Offset、消息去重等机制，确保消息不丢失、不重复处理。
- **可扩展性**: 采用多进程架构，方便横向扩展消费者实例以应对高负载。
- **易用性**: 封装底层 `aiokafka` 细节，提供简洁的 API 和基于配置文件的灵活部署方式。

## 2. 架构设计

Kafka 服务采用分层设计，各模块职责清晰，易于维护和扩展。

```mermaid
graph TD
    subgraph 应用层
        A[KafkaProcessManager] --> B{KafkaService};
        C[业务逻辑] --> B;
    end

    subgraph 服务层
        B --> D[KafkaConsumerService];
        B --> E[KafkaProducerService];
    end

    subgraph 依赖服务
        D --> F[RedisOffsetManager];
        D --> G[RedisDeduplicationService];
        D --> H[AIOKafkaConsumer];
        E --> I[AIOKafkaProducer];
    end

    subgraph 外部系统
        H --> J[Kafka Broker];
        I --> J;
        F --> K[Redis];
        G --> K;
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#ccf,stroke:#333,stroke-width:2px
```

### 2.1. 核心组件

| 组件 | 文件 | 核心职责 |
| :--- | :--- | :--- |
| **KafkaProcessManager** | `kafka_process_manager.py` | **进程管理器**：负责根据配置启动、监控和优雅关闭多个消费者进程。是服务的入口。 |
| **KafkaService** | `kafka_service.py` | **服务协调器**：集成消费者和生产者，管理单个消费进程的完整生命周期，协调消息处理、结果发送和 Offset 提交。 |
| **KafkaConsumerService**| `kafka_consumer_service.py` | **消费者核心**：封装 `AIOKafkaConsumer`，负责消息拉取、Offset 管理、Rebalance 处理和健康检查。 |
| **KafkaProducerService**| `kafka_producer_service.py` | **生产者核心**：封装 `AIOKafkaProducer`，负责将处理结果高效、可靠地发送回 Kafka。 |
| **RedisOffsetManager** | `redis_service.py` | **Offset 备份服务**：将消费者组的 Offset 备份到 Redis，防止 Kafka `__consumer_offsets` 主题数据丢失或 Rebalance 期间的 Offset 重置问题。 |
| **RedisDeduplicationService**| `redis_service.py` | **消息去重服务**：通过在 Redis 中记录已处理的消息 ID，防止因重试或故障恢复导致的消息重复处理。 |

## 3. 工作流程

### 3.1. 启动流程

1.  `KafkaProcessManager` 作为主入口，根据配置决定启动多少个消费者进程。
2.  每个子进程中，实例化一个 `KafkaService` 对象。
3.  `KafkaService` 根据配置初始化 `KafkaConsumerService` 和 `KafkaProducerService`。
4.  如果配置启用，`KafkaService` 会初始化 `RedisOffsetManager` 和 `RedisDeduplicationService`。
5.  `KafkaService` 启动消费者循环 (`start_consumer_loop`)，该循环中会启动底层的 `AIOKafkaConsumer` 和 `AIOKafkaProducer`。

### 3.2. 消息消费与处理流程

```mermaid
sequenceDiagram
    participant Consumer as KafkaConsumerService
    participant Service as KafkaService
    participant Processor as 业务处理器
    participant Producer as KafkaProducerService
    participant Redis

    loop 消费循环
        Consumer->>Service: 拉取一批消息 get_messages()
        Service->>Redis: (可选) 过滤重复消息 get_processable_messages()
        Redis-->>Service: 返回可处理的消息
        Service->>Processor: 异步并发处理消息 process_messages()
        Processor-->>Service: 返回处理结果
        Service->>Producer: (可选) 发送结果 produce_results()
        Producer-->>Service: 返回发送成功的消息
        Service->>Redis: (可选) 标记已处理消息 batch_mark_messages_processed()
        Service->>Consumer: 提交Offset commit_offsets()
        Service->>Redis: (可选) 备份Offset到Redis _save_offsets_to_redis()
    end
```

1.  **拉取消息**: `KafkaConsumerService` 通过 `getmany()` 批量从 Kafka 拉取消息。
2.  **消息去重**: `KafkaService` 调用 `RedisDeduplicationService` 过滤掉已经处理过的消息。
3.  **并发处理**: `KafkaService` 使用 `asyncio.gather` 并发调用业务处理器 (`message_processor`) 处理消息。
4.  **结果发送**: 如果配置了 `produce_topic`，`KafkaProducerService` 会将处理结果批量发送到指定的 Topic。
5.  **标记完成**: `KafkaService` 将成功处理并发送结果的消息 ID 批量写入 Redis，用于去重。
6.  **提交 Offset**: `KafkaConsumerService` 将该批次所有消息（无论处理成功与否）的最大 Offset 提交到 Kafka。
7.  **备份 Offset**: 同时，该 Offset 也会被备份到 Redis，用于灾难恢复。

### 3.3. 故障恢复与健壮性

-   **连接错误**: `KafkaConsumerService` 和 `KafkaProducerService` 会捕获 `CONNECTION_REBUILD_ERRORS` 类型的连接异常，并触发带指数退避的自动重启逻辑 (`restart`)。
-   **Rebalance**:
    -   `AIOConsumerRebalanceListener` 监听 Rebalance 事件。
    -   `on_partitions_revoked`: 分区被撤销时记录日志。
    -   `on_partitions_assigned`: 分区被重新分配后，会暂停消费，并延迟一段时间（`partitions_assigned_delay`）等待集群稳定。然后从 Kafka 或 Redis 中获取有效的 Offset，恢复消费位置，避免从头消费或丢失进度。
-   **健康检查**: `KafkaConsumerService` 的 `check_liveness` 方法会定期检查与 Broker 的连接状态。如果长时间未拉取到消息，会主动检查连接，若失败则触发重启。
-   **优雅关闭**: `KafkaProcessManager` 和 `KafkaService` 注册了 `SIGINT` 和 `SIGTERM` 信号处理器。收到关闭信号后，会停止拉取新消息，等待当前批次处理完成，然后清理资源，确保数据不丢失。

## 4. 使用说明

### 4.1. 启动消费者

通过 `KafkaProcessManager` 启动消费进程。

```python
# service.py
class KafkaManager(Base):
    """
    kafka 消费者服务管理
    """
    WORKERS = int(config["WORKERS"]["kafka"])
    CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
    PID_FILE = f"{CURRENT_PATH}/logs/kafka.pid"
    KAFKA_CMD = ['python', '-c', f'from services.kafka.kafka_process_manager import KafkaProcessManager; KafkaProcessManager.start_processes({WORKERS})']

    @classmethod
    def start(cls):
        """启动服务"""
        if cls.is_running(cls.PID_FILE):
            print("Kafka service is already running")
            return

        try:
            with open(cls.PID_FILE, 'w') as f:
                process = subprocess.Popen(cls.KAFKA_CMD, stdout=subprocess.PIPE, stderr=subprocess.PIPE, preexec_fn=os.setsid)
                f.write(str(process.pid))
            print(f"Kafka service started, PID: {process.pid}")
        except Exception as e:
            print(f"Failed to start kafka service: {e}")

    @classmethod
    def stop(cls):
        """停止服务"""
        pid = cls.get_pid(cls.PID_FILE)
        if pid:
            try:
                os.killpg(os.getpgid(pid), signal.SIGTERM)
                # 增加等待时间以允许 Kafka 消费者优雅关闭
                graceful_shutdown_timeout = 30
                wait_interval = 1
                elapsed_time = 0
                while cls.check_pid(pid) and elapsed_time < graceful_shutdown_timeout:
                    time.sleep(wait_interval)
                    elapsed_time += wait_interval
                    print(f"Waiting for Kafka service (PID: {pid}) to terminate... {elapsed_time}/{graceful_shutdown_timeout}s")

                if cls.check_pid(pid):
                    print(f"Kafka service (PID: {pid}) did not terminate gracefully after {graceful_shutdown_timeout}s. Forcing shutdown with SIGKILL.")
                    # 还活着，强制杀死
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                os.remove(cls.PID_FILE)
                print(f"Kafka service stopped, PID: {pid}")
            except Exception as e:
                print(f"Failed to stop kafka service: {e}")
        else:
            print("Kafka service is not running")

    @classmethod
    def restart(cls):
        cls.stop()
        cls.start()
```

### 4.2. 配置文件 (`config.ini`)

以下是 Kafka 服务的必要配置项。请将它们添加到您的 `config.ini` 文件中。

```ini
[KAFKA]
# Kafka Broker 地址，多个用逗号分隔
bootstrap_servers = kafka-1:9092,kafka-2:9092

# ======== 消费组配置 (可根据项目需求配置多组) ========
# ========= 主观题批改消费配置 =========
group_id = essay-correction-group
insp_data_topic = topic-essay-correction-input
insp_data_result_topic = topic-essay-correction-output

# ========= 答案要点生成消费配置 =========
point_group_id = answer-point-group
insp_answer_point_topic = topic-answer-point-input
insp_answer_point_result_topic = topic-answer-point-output

# ========= 消费者初始化参数 (以下为默认值，可根据项目需求配置) =========
# 消费起始位置策略: earliest 或 latest
auto_offset_reset = latest
# 单次最多拉取消息数
max_poll_records = 1
# 消费者 session 超时时间 (ms)
session_timeout_ms = 30000
# poll 间最大允许处理间隔 (ms)
max_poll_interval_ms = 900000
# 心跳间隔 (ms)
heartbeat_interval_ms = 5000

# ========= 消费者业务参数 (以下为默认值，可根据项目需求配置) =========
# 拉取（poll）消息的最长等待时间(ms)
batch_timeout_ms = 1000
# 消费者健康检查间隔 (s)
alive_check_interval = 300
# 消费者空闲时定期提交 Offset 的间隔 (s)
commit_interval = 1800
# 分区被分配后延迟消费时间(s)
partitions_assigned_delay = 30
# 连续空消息检查阈值
empty_poll_threshold = 30

# ========= 生产者初始化参数 (以下为默认值，可根据项目需求配置) =========
# 消息确认策略: all / 1 / 0
acks = all
# 压缩类型: none / gzip / snappy / lz4 / zstd
compression_type = gzip
# 批量发送最大大小 (字节)
max_batch_size = 262144
# 批量等待延迟 (ms)
linger_ms = 20

# ========= Redis 集成功能 (以下为默认值，可根据项目需求配置)  =========
# 是否使用 Redis 存储 offset
use_redis_offset_manager = false
# 是否启用 Redis 去重功能
use_redis_deduplication_service = false

# ========= 优雅关闭配置 (以下为默认值，可根据项目需求配置) =========
# 等待当前批次完成的最大时间 (s)
graceful_shutdown_timeout = 15
# 是否超时后强制退出（true 表示超时后取消当前批次任务，立即进入清理阶段；false 表示等待当前批次处理完成后再退出）
force_shutdown_on_timeout = true
# 主进程清理子进程的最大等待时间 (s)
main_process_cleanup_timeout = 20
```

### 4.3. Redis 配置

请确保 `config.ini` 中已正确配置 Redis 连接信息，因为 `RedisOffsetManager` 和 `RedisDeduplicationService` 依赖于此。

```ini
[REDIS]
# Redis 主机地址（IP 或域名）
host = your_redis_host
# Redis 端口号（默认6379）
port = 6379
# Redis 数据库索引（通常 0 ~ 15）
db = 0
# Redis 密码（若未设置可留空）
password = your_redis_password
# 消费者偏移量的 TTL（单位：天）
offset_ttl_days = 14
# 去重数据的 TTL（单位：天）
deduplication_ttl_days = 1
```

## 5. 总结

该 Kafka 服务通过分层设计、多进程架构和全面的健壮性策略，为项目提供了可靠、高性能的异步消息处理基础。开发者只需关注业务逻辑的实现，并通过配置文件即可轻松部署和扩展服务。
