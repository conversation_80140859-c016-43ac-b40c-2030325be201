import os
import time
import asyncio
from utils.logger import logger
from config.settings import config
from typing import Optional, List, Any, Dict
from .errors import CONNECTION_REBUILD_ERRORS
from aiokafka.errors import CommitFailedError
from aiokafka import AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener
from services.redis_service import RedisOffsetManager, RedisDeduplicationService
from aiokafka.coordinator.assignors.sticky.sticky_assignor import StickyPartitionAssignor


class AIOConsumerRebalanceListener(ConsumerRebalanceListener):
    """Rebalance事件监听器"""
    def __init__(self, on_revoke_cb: Any, on_assign_cb: Any) -> None:
        self.on_revoke_cb = on_revoke_cb
        self.on_assign_cb = on_assign_cb
    
    async def on_partitions_revoked(self, revoked: Any) -> None:
        """分区被撤销时的回调"""
        try:
            if self.on_revoke_cb:
                await self.on_revoke_cb(revoked)
        except Exception as e:
            logger.error(f"Error in on_partitions_revoked callback: {e}")
    
    async def on_partitions_assigned(self, assigned: Any) -> None:
        """分区被分配时的回调"""
        try:
            if self.on_assign_cb:
                await self.on_assign_cb(assigned)
        except Exception as e:
            logger.error(f"Error in on_partitions_assigned callback: {e}")


class KafkaConsumerService:
    """Kafka消费者服务"""
    
    SERVICE_ONLY_PARAMS: set[str] = {
        'batch_timeout_ms', 'alive_check_interval', 'commit_interval',
        'partitions_assigned_delay', 'empty_poll_threshold'
    }
    
    KAFKA_CONSUMER_PARAMS: set[str] = {
        'auto_offset_reset',
        'max_poll_records',
        'session_timeout_ms', 
        'max_poll_interval_ms', 
        'heartbeat_interval_ms'
    }
    
    def __init__(self, 
                 bootstrap_servers: str, 
                 group_id: str, 
                 topics: List[str], 
                 redis_offset_manager: Optional[RedisOffsetManager] = None,
                 redis_deduplication_service: Optional[RedisDeduplicationService] = None,
                 **kwargs: Any) -> None:
        """构造函数：接收所有参数并初始化状态"""
        self.bootstrap_servers = bootstrap_servers
        self.group_id = group_id
        self.topics = topics
        self.redis_offset_manager = redis_offset_manager
        self.redis_deduplication_service = redis_deduplication_service
        
        # 将kwargs中的业务参数和Kafka参数分离并设置
        defaults = self._get_default_config()
        self.batch_timeout_ms: int = kwargs.get('batch_timeout_ms', defaults['batch_timeout_ms'])
        self.alive_check_interval: int = kwargs.get('alive_check_interval', defaults['alive_check_interval'])
        self.commit_interval: int = kwargs.get('commit_interval', defaults['commit_interval'])
        self.partitions_assigned_delay: int = kwargs.get('partitions_assigned_delay', defaults['partitions_assigned_delay'])
        self.empty_poll_threshold: int = kwargs.get('empty_poll_threshold', defaults['empty_poll_threshold'])
        self.auto_offset_reset: str = kwargs.get('auto_offset_reset', defaults['auto_offset_reset'])
        self._kafka_config: Dict[str, Any] = {k: v for k, v in kwargs.items() if k in self.KAFKA_CONSUMER_PARAMS}

        self._consumer: Optional[AIOKafkaConsumer] = None
        self._lock: asyncio.Lock = asyncio.Lock()
        self.last_alive_check_time: float = time.time()
        self.last_idle_commit_time: float = time.time()
        self.shutdown_by_signal: bool = False
        self.instance_log_prefix: str = f"[KafkaConsumer|pid:{os.getpid()}|{group_id}|{','.join(topics)}] "

    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'auto_offset_reset': 'latest',
            'max_poll_records': 1,
            'batch_timeout_ms': 1000,
            'session_timeout_ms': 30000,
            'max_poll_interval_ms': 900000,
            'heartbeat_interval_ms': 5000,
            'alive_check_interval': 300,
            'commit_interval': 1800,
            'partitions_assigned_delay': 30,
            'empty_poll_threshold': 30
        }

    @classmethod
    def from_config(cls, 
                    group_id: str, 
                    topics: List[str], 
                    redis_offset_manager: Optional[RedisOffsetManager] = None,
                    redis_deduplication_service: Optional[RedisDeduplicationService] = None,
                    **override_config: Any) -> 'KafkaConsumerService':
        """工厂方法：从全局配置创建消费者服务实例"""
        kafka_config: Dict[str, Any] = config.get("KAFKA", {})
        defaults = cls._get_default_config()
        
        # 合并配置：默认值 < 全局配置 < 覆盖值
        base_config: Dict[str, Any] = {}
        all_param_keys = cls.SERVICE_ONLY_PARAMS | cls.KAFKA_CONSUMER_PARAMS
        for key in all_param_keys:
            default_value = defaults.get(key)
            config_value = kafka_config.get(key, default_value)
            if config_value is not None:
                if isinstance(default_value, bool):
                    base_config[key] = config_value is True
                elif isinstance(default_value, int):
                    base_config[key] = int(config_value)
                elif isinstance(default_value, float):
                    base_config[key] = float(config_value)
                else:
                    base_config[key] = config_value

        final_config = {**base_config, **override_config}

        return cls(
            bootstrap_servers=kafka_config.get("bootstrap_servers"),
            group_id=group_id,
            topics=topics,
            redis_offset_manager=redis_offset_manager,
            redis_deduplication_service=redis_deduplication_service,
            **final_config
        )

    async def start(self) -> None:
        """创建并启动消费者实例"""
        async with self._lock:
            if self._consumer:
                logger.info(f"{self.instance_log_prefix}Consumer is already running.")
                return

            logger.info(f"{self.instance_log_prefix}Starting consumer...")
            try:
                self._consumer = AIOKafkaConsumer(
                    *self.topics,
                    bootstrap_servers=self.bootstrap_servers,
                    group_id=self.group_id,
                    enable_auto_commit=False,
                    partition_assignment_strategy=[StickyPartitionAssignor],
                    **self._kafka_config
                )
                listener = AIOConsumerRebalanceListener(
                    on_revoke_cb=self._on_partitions_revoked,
                    on_assign_cb=self._on_partitions_assigned
                )
                self._consumer.subscribe(self.topics, listener=listener)
                await self._consumer.start()
                logger.info(f"{self.instance_log_prefix}Consumer started successfully.")
                logger.debug(f"{self.instance_log_prefix}Consumer config: {self._kafka_config}")
            except Exception as e:
                logger.error(f"{self.instance_log_prefix}Failed to start consumer: {e}", exc_info=True)
                self._consumer = None
                raise

    async def stop(self) -> None:
        """停止并清理消费者实例"""
        async with self._lock:
            if not self._consumer:
                return
            logger.info(f"{self.instance_log_prefix}Stopping consumer...")
            try:
                await self._consumer.stop()
            except Exception as e:
                logger.error(f"{self.instance_log_prefix}Error stopping consumer: {e}", exc_info=True)
            finally:
                self._consumer = None
                logger.info(f"{self.instance_log_prefix}Consumer stopped.")

    async def restart(self, max_retries: int = 3, initial_delay: int = 5) -> None:
        """带指数退避的重启逻辑"""
        logger.info(f"{self.instance_log_prefix}Restarting service...")
        await self.stop()

        for attempt in range(max_retries):
            try:
                await self.start()
                logger.info(f"{self.instance_log_prefix}Service restarted successfully on attempt {attempt + 1}.")
                return
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"{self.instance_log_prefix}All {max_retries} restart attempts failed. Last error: {e}")
                    raise
                
                backoff_delay = initial_delay * (2 ** attempt)
                logger.warning(f"{self.instance_log_prefix}Restart attempt {attempt + 1} failed. Retrying in {backoff_delay}s... Error: {e}")
                await asyncio.sleep(backoff_delay)

    def _get_client(self) -> AIOKafkaConsumer:
        """获取内部的AIOKafkaConsumer实例，如果未运行则抛出异常"""
        if not self._consumer:
            raise ConnectionError("Consumer is not running or has been stopped.")
        return self._consumer

    async def _on_partitions_revoked(self, revoked: Any) -> None:
        """分区被撤销时的回调"""
        logger.warning(f"{self.instance_log_prefix}Partitions revoked: {revoked}")
        if self.shutdown_by_signal:
            logger.info(f"{self.instance_log_prefix}Shutdown by signal")
            return

    async def _on_partitions_assigned(self, assigned: Any) -> None:
        """分区被分配时的回调"""
        try:
            logger.warning(f"{self.instance_log_prefix}Partitions assigned: {assigned}")
            consumer = self._get_client()
            consumer.pause(*assigned)
            logger.info(f"{self.instance_log_prefix}Waiting {self.partitions_assigned_delay}s to stabilize before consumption.")
            asyncio.create_task(self._verify_and_set_initial_offsets(assigned))
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Error in _on_partitions_assigned: {e}")

    async def _verify_and_set_initial_offsets(self, assigned: Any) -> None:
        """验证并设置初始offset"""
        try:
            await asyncio.sleep(self.partitions_assigned_delay)
            consumer = self._get_client()
            current_assignment = consumer.assignment()
            valid_assignment = [tp for tp in assigned if tp in current_assignment]
            
            if not valid_assignment:
                logger.warning(f"{self.instance_log_prefix}No valid assignment found after delay, skipping offset verification.")
                return
            
            for tp in valid_assignment:
                await self._set_partition_offset(tp)

            logger.info(f"{self.instance_log_prefix}Resuming valid partitions: {valid_assignment}")
            consumer.resume(*valid_assignment)
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Error in verify and set initial offsets: {e}")

    async def _set_partition_offset(self, tp: Any) -> None:
        """为单个分区设置合适的偏移量"""
        try:
            consumer = self._get_client()

            committed_offset = await consumer.committed(tp)
            earliest = (await consumer.beginning_offsets([tp]))[tp]
            latest = (await consumer.end_offsets([tp]))[tp]

            # 判断 Kafka offset 是否有效
            if committed_offset is not None and earliest <= committed_offset <= latest:
                logger.info(
                    f"{self.instance_log_prefix}Offset decision for {tp} | "
                    f"selected=kafka:{committed_offset} | "
                    f"range=[{earliest}, {latest}] | "
                    f"sources={{kafka:{committed_offset}, redis:SKIPPED}} | "
                    f"use_redis_offset_manager={bool(self.redis_offset_manager)}"
                )
                return  # Kafka offset 有效，自动恢复，不需要 seek

            # Kafka offset 无效，尝试 Redis
            redis_offset = await self._get_offset_from_redis(tp)
            if redis_offset is not None and earliest <= redis_offset <= latest:
                await consumer.seek(tp, redis_offset)
                logger.info(
                    f"{self.instance_log_prefix}Offset decision for {tp} | "
                    f"selected=redis:{redis_offset} | "
                    f"range=[{earliest}, {latest}] | "
                    f"sources={{kafka:{committed_offset}, redis:{redis_offset}}} | "
                    f"use_redis_offset_manager={bool(self.redis_offset_manager)}"
                )
            else:
                logger.warning(
                    f"{self.instance_log_prefix}Offset decision for {tp} | "
                    f"no valid offset found, using auto_offset_reset='{self.auto_offset_reset}' | "
                    f"range=[{earliest}, {latest}] | "
                    f"sources={{kafka:{committed_offset}, redis:{redis_offset}}} | "
                    f"use_redis_offset_manager={bool(self.redis_offset_manager)}"
                )
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to verify/set offset for {tp}: {e}")

    async def _get_offset_from_redis(self, tp: Any) -> Optional[int]:
        """从 Redis 获取分区的备份 offset"""
        if not self.redis_offset_manager:
            return None
        try:
            return await self.redis_offset_manager.get_offset(self.group_id, tp.topic, tp.partition)
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to get offset from Redis for {tp}: {e}")
            return None

    async def mark_message_processed(self, message: Any) -> None:
        """标记消息为已处理"""
        if not self.redis_deduplication_service or not message:
            return
        try:
            await self.redis_deduplication_service.mark_message_processed(self.group_id, message)
            logger.debug(f"{self.instance_log_prefix}Marked message as processed")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to mark message as processed: {e}")

    async def batch_mark_messages_processed(self, messages: List[Any]) -> None:
        """批量在 Redis 中标记消息为已处理"""
        if not self.redis_deduplication_service or not messages:
            return
        try:
            await self.redis_deduplication_service.batch_mark_messages_processed(self.group_id, messages)
            logger.info(f"{self.instance_log_prefix}Batch marked {len(messages)} messages as processed.")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to batch mark {len(messages)} messages as processed: {e}")

    async def check_liveness(self) -> bool:
        """检查消费者是否还活着"""
        try:
            if time.time() - self.last_alive_check_time < self.alive_check_interval:
                return True

            consumer = self._get_client()
            assigned_partitions = consumer.assignment()
            if assigned_partitions:
                tp = next(iter(assigned_partitions))
                await asyncio.wait_for(consumer.position(tp), timeout=10.0)
            else:
                logger.warning(f"{self.instance_log_prefix}Consumer has no assigned partitions, skipping position check.")
            
            self.last_alive_check_time = time.time()
            logger.debug(f"{self.instance_log_prefix}Consumer liveness check passed.")
            return True
        except asyncio.TimeoutError:
            logger.error(f"{self.instance_log_prefix}Consumer liveness check timed out.")
            return False
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Unexpected liveness check error: {e}")
            return False

    async def periodic_commit_idle(self) -> None:
        """无消息时定期提交偏移量"""
        try:
            if time.time() - self.last_idle_commit_time > self.commit_interval:
                consumer = self._get_client()
                if consumer.assignment():
                    await consumer.commit()
                    logger.debug(f"{self.instance_log_prefix}Periodic commit successful")
                else:
                    logger.debug(f"{self.instance_log_prefix}Periodic commit skipped: no assigned partitions")
                self.last_idle_commit_time = time.time()
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Periodic commit failed: {e}")

    async def get_messages(self):
        """批量拉取消息"""
        try:
            consumer = self._get_client()
            messages = await consumer.getmany(timeout_ms=self.batch_timeout_ms)
            if messages:
                self.last_alive_check_time = time.time()
            return messages
        except (ConnectionError, *CONNECTION_REBUILD_ERRORS) as e:
            logger.error(f"{self.instance_log_prefix}Consumer is unhealthy: {e}. Triggering self-restart.", exc_info=True)
            await self.restart()
            raise
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to consume messages: {e}", exc_info=True)
            raise

    async def get_processable_messages(self, messages: List[Any]) -> List[Any]:
        """过滤掉在 Redis 中已被标记为处理过的重复消息"""
        if not self.redis_deduplication_service or not messages:
            return messages
        try:
            batch_status = await self.redis_deduplication_service.batch_check_messages(self.group_id, messages)
            processable_messages: List[Any] = []
            for message in messages:
                message_id = f"{self.group_id}:{message.topic}:{message.partition}:{message.offset}"
                if not batch_status.get(message_id, False):
                    processable_messages.append(message)
            return processable_messages
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to get processable messages from Redis: {e}. Processing all messages as a fallback.")
            return messages

    async def commit_single_partition_offset(self, tp: Any, offset: int) -> None:
        """提交单个分区的偏移量"""
        try:
            consumer = self._get_client()
            await consumer.commit({tp: offset + 1})
            self.last_idle_commit_time = time.time()
            committed_offset = await consumer.committed(tp)
            logger.info(f"{self.instance_log_prefix}Committed successfully, tp: {tp}, offset: {offset}, committed_offset: {committed_offset}")
        except CommitFailedError as cfe:
            logger.warning(f"{self.instance_log_prefix}Commit failed, tp: {tp}, offset: {offset}, likely due to rebalance: {cfe}")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to commit offset for {tp}, offset: {offset}, error: {e}")
            raise

    async def _get_max_offsets(self, messages: List[Any]):
        """计算每个分区的最大offset"""
        partitions: Dict[Any, int] = {}
        for message in messages:
            tp = TopicPartition(message.topic, message.partition)
            if tp not in partitions or message.offset > partitions[tp]:
                partitions[tp] = message.offset
        return partitions

    async def _save_offsets_to_redis(self, offsets: Dict[Any, int]) -> None:
        """将偏移量批量备份到Redis"""
        if not self.redis_offset_manager or not offsets:
            return
        try:
            next_offsets: Dict[Any, int] = {}
            for tp, offset in offsets.items():
                key = (tp.topic, tp.partition)
                next_offsets[key] = offset + 1
            await self.redis_offset_manager.batch_save_offsets_lua(self.group_id, next_offsets)
            logger.info(f"{self.instance_log_prefix}Successfully saved {len(next_offsets)} next offsets to Redis: {next_offsets}")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Failed to save offsets to Redis: {offsets}, error: {e}")

    async def commit_offsets(self, messages: List[Any]) -> None:
        """提交偏移量"""
        if not messages:
            return
        
        max_offsets = await self._get_max_offsets(messages)
        
        if self.redis_offset_manager:
            await self._save_offsets_to_redis(max_offsets)
        
        try:
            consumer = self._get_client()
            commit_dict = {tp: offset + 1 for tp, offset in max_offsets.items()}
            await consumer.commit(commit_dict)
            self.last_idle_commit_time = time.time()
            logger.info(f"{self.instance_log_prefix}Batch committed {len(commit_dict)} partitions successfully")
        except CommitFailedError as cfe:
            logger.warning(f"{self.instance_log_prefix}Batch commit failed, likely due to rebalance: {cfe}")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Batch commit failed: {e}")