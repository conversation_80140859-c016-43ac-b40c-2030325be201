import os
import time
import signal
import asyncio
import threading
from utils.logger import logger
from config.settings import config
from multiprocessing import Process
from .kafka_service import KafkaService
from services import AI_essay_answer_point, AI_essay_correction


class KafkaProcessManager:
    """Kafka进程管理器 - 专门负责多进程启动和管理"""
    
    # 类变量用于进程管理
    _signal_received = False
    _signal_lock = threading.Lock()
    _shutdown_event = threading.Event()
    _processes = []
    log_prefix = f"[KafkaProcessManager|pid:{os.getpid()}] "
    
    @classmethod
    def _create_and_run_consumer(cls, group_id: str, topics: list, produce_topic: str, message_processor, **kwargs):
        """创建并运行消费者进程"""
        try:
            # 创建服务实例，传递具体参数
            kafka_service = KafkaService(
                group_id=group_id,
                topics=topics,
                produce_topic=produce_topic,
                **kwargs
            )
            
            # 运行消费循环
            async def run_consumer():
                await kafka_service.start_consumer_loop(
                    message_processor
                )
            
            asyncio.run(run_consumer())
        except Exception as e:
            logger.error(f"{cls.log_prefix}Consumer process crashed: {e}", exc_info=True)
    
    @classmethod
    def start_consuming(cls):
        """启动主观题批改消费进程"""
        settings = config["KAFKA"]
        group_id = settings["group_id"]
        topics = [settings["insp_data_topic"]]
        produce_topic = settings["insp_data_result_topic"]
        max_poll_records = int(settings.get("max_poll_records", 1))
        use_redis_offset_manager = settings.get("use_redis_offset_manager", False) is True
        use_redis_deduplication_service = settings.get("use_redis_deduplication_service", False) is True
        graceful_shutdown_timeout = int(settings.get("graceful_shutdown_timeout", 15))
        force_shutdown_on_timeout = settings.get("force_shutdown_on_timeout", True) is True
        
        cls._create_and_run_consumer(
            group_id=group_id,
            topics=topics,
            produce_topic=produce_topic,
            message_processor=AI_essay_correction.run,
            max_poll_records=max_poll_records,
            use_redis_offset_manager=use_redis_offset_manager,
            use_redis_deduplication_service=use_redis_deduplication_service,
            graceful_shutdown_timeout=graceful_shutdown_timeout,
            force_shutdown_on_timeout=force_shutdown_on_timeout
        )
    
    @classmethod
    def start_answer_point_consuming(cls):
        """启动答案要点生成消费进程"""
        settings = config["KAFKA"]
        group_id = settings["point_group_id"]
        topics = [settings["insp_answer_point_topic"]]
        produce_topic = settings["insp_answer_point_result_topic"]
        max_poll_records = int(settings.get("max_poll_records", 1))
        use_redis_offset_manager = settings.get("use_redis_offset_manager", False) is True
        use_redis_deduplication_service = settings.get("use_redis_deduplication_service", False) is True
        graceful_shutdown_timeout = int(settings.get("graceful_shutdown_timeout", 15))
        force_shutdown_on_timeout = settings.get("force_shutdown_on_timeout", True) is True
        
        cls._create_and_run_consumer(
            group_id=group_id,
            topics=topics,
            produce_topic=produce_topic,
            message_processor=AI_essay_answer_point.run,
            max_poll_records=max_poll_records,
            use_redis_offset_manager=use_redis_offset_manager,
            use_redis_deduplication_service=use_redis_deduplication_service,
            graceful_shutdown_timeout=graceful_shutdown_timeout,
            force_shutdown_on_timeout=force_shutdown_on_timeout
        )

    @classmethod
    def _register_signal_handlers(cls):
        def handle_main_signal(signum, _):
            with cls._signal_lock:
                if cls._signal_received:
                    logger.debug(f"{cls.log_prefix}Signal {signum} already handled, ignoring duplicate")
                    return
                
                cls._signal_received = True
                logger.info(f"{cls.log_prefix}Received signal {signum}, initiating shutdown...")
                cls._shutdown_event.set()

        signal.signal(signal.SIGINT, handle_main_signal)
        signal.signal(signal.SIGTERM, handle_main_signal)

    @classmethod
    def _monitor_processes(cls):
        """监控进程状态和信号量"""
        logger.info(f"{cls.log_prefix}Starting process monitoring...")

        dead_pids = set()  # 记录已经死亡并报告过的进程PID
        while not cls._shutdown_event.is_set():
            for process in cls._processes:
                process_pid = process.pid
                if not process.is_alive() and process_pid not in dead_pids:
                    logger.error(f"{cls.log_prefix}Process {process_pid} died unexpectedly")
                    dead_pids.add(process_pid)

            time.sleep(1)

        logger.info(f"{cls.log_prefix}Process monitoring ended")

    @classmethod
    def _cleanup_processes(cls, timeout):
        """清理所有子进程（先优雅退出，后强杀）"""
        logger.info(f"{cls.log_prefix}Requesting graceful shutdown of all processes")
        cls._shutdown_event.set()

        if not cls._processes:
            logger.info(f"{cls.log_prefix}No processes to clean up")
            return

        cls._send_signal_to_processes(signal.SIGTERM)

        start_time = time.time()
        logger.info(f"{cls.log_prefix}Waiting up to {timeout}s for processes to exit gracefully")

        cls._wait_for_processes_graceful(timeout)

        alive_processes = cls._get_alive_processes()
        if alive_processes:
            logger.warning(f"{cls.log_prefix}Graceful shutdown timeout. Forcing kill on {len(alive_processes)} remaining processes")
            cls._kill_processes(alive_processes)

        success_count = len(cls._processes) - len(cls._get_alive_processes())
        elapsed = time.time() - start_time

        logger.info(f"{cls.log_prefix}{success_count}/{len(cls._processes)} processes cleaned up successfully in {elapsed:.2f}s")

    @classmethod
    def _send_signal_to_processes(cls, sig):
        """向所有子进程发送指定信号"""
        for process in cls._processes:
            if process.is_alive():
                try:
                    os.kill(process.pid, sig)
                    logger.info(f"{cls.log_prefix}Sent signal {sig} to PID {process.pid}")
                except ProcessLookupError:
                    logger.warning(f"{cls.log_prefix}Process {process.pid} already exited")
                except Exception as e:
                    logger.error(f"{cls.log_prefix}Failed to send signal {sig} to PID {process.pid}: {e}")

    @classmethod
    def _wait_for_processes_graceful(cls, timeout):
        """等待子进程在限定时间内优雅退出"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if not cls._get_alive_processes():
                logger.info(f"{cls.log_prefix}All processes exited gracefully")
                return
            time.sleep(max(0, min(1, timeout - (time.time() - start_time))))
        
    @classmethod
    def _kill_processes(cls, processes):
        """强制终止进程"""
        for process in processes:
            try:
                if process.is_alive():
                    logger.warning(f"{cls.log_prefix}Killing process {process.pid}")
                    process.kill()
                    process.join()
            except Exception as e:
                logger.error(f"{cls.log_prefix}Error killing process {process.pid}: {e}")

    @classmethod
    def _get_alive_processes(cls):
        return [p for p in cls._processes if p.is_alive()]
    
    @classmethod
    def start_processes(cls, max_threshold=1):
        """启动进程"""
        kafka_config = config.get("KAFKA", {})
        main_process_cleanup_timeout = int(kafka_config.get("main_process_cleanup_timeout", 20))

        cls._register_signal_handlers()

        try:
            for _ in range(max_threshold):
                p = Process(target=cls.start_consuming)
                p.start()
                cls._processes.append(p)
                logger.info(f"{cls.log_prefix}Started consumer process {p.pid}")

            for _ in range(2):
                p = Process(target=cls.start_answer_point_consuming)
                p.start()
                cls._processes.append(p)
                logger.info(f"{cls.log_prefix}Started answer point consumer process {p.pid}")

            logger.info(f"{cls.log_prefix}All {len(cls._processes)} processes started")

            # 监控主循环
            cls._monitor_processes()

        except Exception as e:
            logger.error(f"{cls.log_prefix}Exception: {e}")
            
        finally:
            cls._cleanup_processes(main_process_cleanup_timeout)