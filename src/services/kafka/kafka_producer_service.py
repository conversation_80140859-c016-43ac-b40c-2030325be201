import os
import json
import asyncio
from utils.logger import logger
from config.settings import config
from aiokafka import AIOKafkaProducer
from typing import Optional, List, Any, Dict
from .errors import CONNECTION_REBUILD_ERRORS


class KafkaProducerService:
    """Kafka生产者服务"""

    KAFKA_PRODUCER_PARAMS: set[str] = {
        'acks', 'compression_type', 'max_batch_size', 'linger_ms'
    }

    def __init__(self, bootstrap_servers: str, **kwargs: Any) -> None:
        """构造函数：接收所有参数并初始化状态"""
        self.bootstrap_servers: str = bootstrap_servers
        self._kafka_config: Dict[str, Any] = kwargs

        self._producer: Optional[AIOKafkaProducer] = None
        self._lock: asyncio.Lock = asyncio.Lock()
        self.log_prefix: str = f"[KafkaProducer|pid:{os.getpid()}] "

    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'acks': 'all',
            'compression_type': 'gzip',
            'max_batch_size': 262144,
            'linger_ms': 20
        }
    
    @classmethod
    def from_config(cls) -> 'KafkaProducerService':
        """工厂方法：从全局配置创建生产者服务实例"""
        kafka_config: Dict[str, Any] = config.get("KAFKA", {})
        defaults = cls._get_default_config()

        # 提取生产者特定参数，并使用默认值兜底
        producer_config: Dict[str, Any] = {}
        for key in cls.KAFKA_PRODUCER_PARAMS:
            default_value = defaults.get(key)
            config_value = kafka_config.get(key, default_value)
            if config_value is not None:
                if isinstance(default_value, bool):
                    producer_config[key] = config_value is True
                elif isinstance(default_value, int):
                    producer_config[key] = int(config_value)
                elif isinstance(default_value, float):
                    producer_config[key] = float(config_value)
                else:
                    producer_config[key] = config_value

        return cls(
            bootstrap_servers=kafka_config.get("bootstrap_servers"),
            **producer_config
        )

    async def start(self) -> None:
        """创建并启动生产者实例"""
        async with self._lock:
            if self._producer:
                logger.info(f"{self.log_prefix}Producer is already running.")
                return

            logger.info(f"{self.log_prefix}Starting producer...")
            try:
                self._producer = AIOKafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8') if isinstance(v, (dict, list)) else str(v).encode('utf-8'),
                    **self._kafka_config
                )
                await self._producer.start()
                logger.info(f"{self.log_prefix}Producer started successfully.")
                logger.debug(f"{self.log_prefix}Producer config: {self._kafka_config}")
            except Exception as e:
                logger.error(f"{self.log_prefix}Failed to start producer: {e}", exc_info=True)
                self._producer = None
                raise

    async def stop(self) -> None:
        """停止并清理生产者实例"""
        async with self._lock:
            if not self._producer:
                return
            logger.info(f"{self.log_prefix}Stopping producer...")
            try:
                await self._producer.stop()
            except Exception as e:
                logger.error(f"{self.log_prefix}Error stopping producer: {e}", exc_info=True)
            finally:
                self._producer = None
                logger.info(f"{self.log_prefix}Producer stopped.")

    async def restart(self, max_retries: int = 3, initial_delay: int = 5) -> None:
        """带指数退避的重启逻辑"""
        logger.info(f"{self.log_prefix}Restarting service...")
        await self.stop()

        for attempt in range(max_retries):
            try:
                await self.start()
                logger.info(f"{self.log_prefix}Service restarted successfully on attempt {attempt + 1}.")
                return
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"{self.log_prefix}All {max_retries} restart attempts failed. Last error: {e}")
                    raise
                
                backoff_delay = initial_delay * (2 ** attempt)
                logger.warning(f"{self.log_prefix}Restart attempt {attempt + 1} failed. Retrying in {backoff_delay}s... Error: {e}")
                await asyncio.sleep(backoff_delay)

    def _get_client(self) -> AIOKafkaProducer:
        """获取内部的AIOKafkaProducer实例，如果未运行则抛出异常"""
        if not self._producer:
            raise ConnectionError("Producer is not running or has been stopped.")
        return self._producer

    async def send_one(self, topic: str, message: Any, key: Optional[bytes] = None):
        """发送单条消息到指定topic"""
        try:
            producer = self._get_client()
            return await producer.send(topic, value=message, key=key)
        except (ConnectionError, *CONNECTION_REBUILD_ERRORS) as e:
            logger.error(f"{self.log_prefix}Producer is unhealthy: {e}. Triggering self-restart.", exc_info=True)
            await self.restart()
            raise
        except Exception as e:
            logger.error(f"{self.log_prefix}Failed to send message to {topic}: {e}", exc_info=True)
            raise

    async def send_batch(self, topic: str, messages: List[Any]):
        """批量发送消息到指定topic"""
        if not messages:
            return []
        try:
            producer = self._get_client()
            tasks = [producer.send(topic, value=msg) for msg in messages]
            return await asyncio.gather(*tasks, return_exceptions=True)
        except (ConnectionError, *CONNECTION_REBUILD_ERRORS) as e:
            logger.error(f"{self.log_prefix}Producer is unhealthy: {e}. Triggering self-restart.", exc_info=True)
            await self.restart()
            raise
        except Exception as e:
            logger.error(f"{self.log_prefix}Batch send failed to send messages to {topic}: {e}", exc_info=True)
            raise
