import os
import json
import signal
import asyncio
import threading
from utils.logger import logger
from typing import List, Dict, Any, Optional
from .errors import CONNECTION_REBUILD_ERRORS
from .kafka_consumer_service import KafkaConsumerService
from .kafka_producer_service import KafkaProducerService
from services.redis_service import RedisOffsetManager, RedisDeduplicationService, cleanup_all_async_redis_services


class KafkaService:
    """Kafka服务 - 集成消息处理和进程管理"""
    def __init__(self, 
                 group_id: str, 
                 topics: List[str], 
                 produce_topic: Optional[str] = None, 
                 graceful_shutdown_timeout: int = 15, 
                 force_shutdown_on_timeout: bool = True,
                 use_redis_offset_manager: bool = False,
                 use_redis_deduplication_service: bool = False,
                 **consumer_config: Any) -> None:
        """初始化KafkaService"""
        self.graceful_shutdown_timeout = graceful_shutdown_timeout
        self.force_shutdown_on_timeout = force_shutdown_on_timeout
        self.use_redis_offset_manager = use_redis_offset_manager
        self.use_redis_deduplication_service = use_redis_deduplication_service

        # Redis服务实例的占位符
        self.redis_offset_manager: Optional[RedisOffsetManager] = None
        self.redis_deduplication_service: Optional[RedisDeduplicationService] = None

        # 使用工厂方法创建服务
        self.consumer_service: KafkaConsumerService = KafkaConsumerService.from_config(
            group_id=group_id,
            topics=topics,
            redis_offset_manager=self.redis_offset_manager,
            redis_deduplication_service=self.redis_deduplication_service,
            **consumer_config
        )
        
        self.produce_topic = produce_topic
        if produce_topic:
            self.producer_service: Optional[KafkaProducerService] = KafkaProducerService.from_config()
        else:
            self.producer_service = None
            
        # 关闭事件
        self._shutdown_event = asyncio.Event()
        self._signal_received = False
        self._signal_lock = threading.Lock()

        self.instance_log_prefix = f"[KafkaService|pid:{os.getpid()}|{group_id}|{','.join(topics)}] "

    async def _initialize_redis_services(self) -> None:
        """根据配置初始化Redis服务"""
        if self.use_redis_offset_manager:
            await RedisOffsetManager.initialize()
            self.redis_offset_manager = RedisOffsetManager
            logger.info(f"{self.instance_log_prefix}RedisOffsetManager initialized by KafkaService.")

        if self.use_redis_deduplication_service:
            await RedisDeduplicationService.initialize()
            self.redis_deduplication_service = RedisDeduplicationService
            logger.info(f"{self.instance_log_prefix}RedisDeduplicationService initialized by KafkaService.")
        
        # 将初始化后的实例更新到消费者服务中
        self.consumer_service.redis_offset_manager = self.redis_offset_manager
        self.consumer_service.redis_deduplication_service = self.redis_deduplication_service

    async def _cleanup_redis_services(self) -> None:
        """清理由KafkaService管理的Redis服务"""
        if self.use_redis_offset_manager or self.use_redis_deduplication_service:
            await cleanup_all_async_redis_services()
            logger.info(f"{self.instance_log_prefix}Redis services cleaned up by KafkaService.")

    @staticmethod
    def _build_message_log_prefix(message: Any) -> str:
        """构建单条消息的日志前缀"""
        try:
            return f"[{message.topic}:{message.partition}:{message.offset}] "
        except Exception as e:
            logger.error(f"Failed to build message log prefix: {e}")
            return "[Unknown] "
    
    @staticmethod
    def _enrich_message_log_prefix(message_prefix: str, data: Any) -> str:
        """根据消息内容丰富日志前缀"""
        try:
            if isinstance(data, dict):
                if "answerId" in data:
                    return message_prefix + f"[answerId:{data.get('answerId')}] "
                elif "questionId" in data:
                    return message_prefix + f"[questionId:{data.get('questionId')}] "
        except Exception as e:
            logger.error(f"Failed to enrich message log prefix: {e}")
        return message_prefix
    
    def _build_batch_log_prefix(self, batch_info: str = "") -> str:
        """构建批次级日志前缀"""
        if batch_info:
            return f"{self.instance_log_prefix}[Batch] {batch_info} - "
        else:
            return f"{self.instance_log_prefix}[Batch] "
            
    async def process_single_message(self, message: Any, message_processor: Any) -> Optional[Any]:
        """处理单条消息"""
        message_prefix = self._build_message_log_prefix(message)
        try:
            data = json.loads(message.value.decode('utf-8'))
            message_prefix = self._enrich_message_log_prefix(message_prefix, data)
            
            logger.info(f"{message_prefix}Start processing message.")
            result, cost_time = await message_processor(data)
            logger.info(f"{message_prefix}Processed message successfully, cost_time: {cost_time}s.")
            return result
        except json.JSONDecodeError as e:
            logger.error(f"{message_prefix}Failed to decode JSON from message {message.value.decode('utf-8')}, error: {e}")
            return None
        except Exception as e:
            logger.error(f"{message_prefix}Failed to process message: {message.value.decode('utf-8')}, error: {e}", exc_info=True)
            return None

    async def process_messages(self, messages: List[Any], message_processor: Any):
        """消息批量处理"""
        if not messages:
            return []
        tasks = [self.process_single_message(message, message_processor) for message in messages]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def process_messages_with_shutdown_monitor(self, messages: List[Any], message_processor: Any, log_prefix: str = ""):
        """消息批量处理，支持关闭监控"""
        if not messages:
            return []

        process_task = asyncio.create_task(self.process_messages(messages, message_processor))
        shutdown_task = asyncio.create_task(self._shutdown_event.wait())
        
        try:
            done, _ = await asyncio.wait(
                {process_task, shutdown_task},
                return_when=asyncio.FIRST_COMPLETED
            )

            if process_task in done:
                return await process_task

            logger.warning(f"{log_prefix}Shutdown signal received. Cancelling processing...")
            process_task.cancel()
            try:
                await process_task
            except asyncio.CancelledError:
                logger.info(f"{log_prefix}Processing task cancelled")
            return None
        
        finally:
            for task in [process_task, shutdown_task]:
                if not task.done():
                    task.cancel()
            await asyncio.gather(process_task, shutdown_task, return_exceptions=True)
        
    async def _filter_valid_results(self, results: List[Any], messages: List[Any], log_prefix: str = ""):
        """过滤有效的处理结果和对应消息"""
        valid_results: List[Any] = []
        valid_messages: List[Any] = []
        
        for result, message in zip(results, messages):
            try:
                current_log_prefix = self._build_message_log_prefix(message)
                if isinstance(result, Exception):
                    logger.error(f"{current_log_prefix}Task failed with error: {result}, Message: {message.value.decode('utf-8')}")
                    continue
                elif result is None:
                    logger.warning(f"{current_log_prefix}No result from processor, Message: {message.value.decode('utf-8')}")
                    continue
                valid_results.append(result)
                valid_messages.append(message)
            except Exception as e:
                logger.error(f"{log_prefix}Failed to filter valid results: {e}")
            
        return valid_results, valid_messages
    
    async def _process_send_results(self, batch_send_results: List[Any], messages: List[Any], results: List[Any], produce_topic: str):
        """处理发送结果"""
        successful_send_messages: List[Any] = []
        for send_result, message, result in zip(batch_send_results, messages, results):
            message_prefix = self._build_message_log_prefix(message)
            try:
                if isinstance(send_result, Exception):
                    logger.error(f"{message_prefix}Failed to send result to {produce_topic}, result: {result}, send_result: {send_result}")
                else:
                    successful_send_messages.append(message)
                    logger.info(f"{message_prefix}Message sent successfully to {produce_topic}")
            except Exception as e:
                logger.error(f"{message_prefix}Failed to process send results, error: {e}")
        
        return successful_send_messages
        
    def _log_send_results_summary(self, results: List[Any], successful_send_messages: List[Any], produce_topic: str, log_prefix: str = ""):
        total_count = len(results)
        successful_count = len(successful_send_messages)
        if successful_count == total_count:
            logger.info(f"{log_prefix}All {total_count} results were successfully sent to {produce_topic}")
        elif successful_count > 0:
            logger.error(f"{log_prefix}Successful send {successful_count}/{total_count} results to {produce_topic}")
        else:
            logger.error(f"{log_prefix}Failed to send {total_count} results to {produce_topic}")

    async def batch_send_results(self, results: List[Any], messages: List[Any], produce_topic: str, log_prefix: str = ""):
        """批量发送方式发送处理结果到目标topic"""
        if not self.producer_service or not produce_topic or not results:
            return []
        
        try:
            logger.info(f"{log_prefix}Sending {len(results)} results to {produce_topic}")
            batch_send_results = await self.producer_service.send_batch(
                topic=produce_topic,
                messages=results
            )
            
            successful_send_messages = await self._process_send_results(batch_send_results, messages, results, produce_topic)

            self._log_send_results_summary(results, successful_send_messages, produce_topic, log_prefix)
            
            return successful_send_messages
        except Exception as e:
            logger.error(f"{log_prefix}Failed to batch send results to {produce_topic}, error: {e}")
            return []

    async def send_results(self, results: List[Any], messages: List[Any], produce_topic: str, log_prefix: str = ""):
        """单条发送方式发送处理结果到目标topic"""
        if not self.producer_service or not produce_topic or not results:
            return []
        
        try:
            logger.info(f"{log_prefix}Sending {len(results)} results to {produce_topic}")
            successful_send_messages = []
            for result, message in zip(results, messages):
                try:
                    await self.producer_service.send_one(produce_topic, result)
                    successful_send_messages.append(message)
                except Exception as e:
                    logger.error(f"{log_prefix}Failed to send result to {produce_topic}, error: {e}, result: {result}")
            
            self._log_send_results_summary(results, successful_send_messages, produce_topic, log_prefix)
            return successful_send_messages
        except Exception as e:
            logger.error(f"{log_prefix}Failed to send results to {produce_topic}, error: {e}")
            return []
        
    async def _commit_safely(self, messages: List[Any], log_prefix: str = "") -> None:
        """安全提交偏移量"""
        try:
            await self.consumer_service.commit_offsets(messages)
        except Exception as e:
            logger.error(f"{log_prefix}Failed to commit offsets: {e}")
        
    async def process_batch_messages(self, messages: Dict[Any, List[Any]], message_processor: Any, produce_topic: Optional[str]) -> None:
        """处理一批次消息"""
        all_messages: List[Any] = []
        batch_log_prefix = self._build_batch_log_prefix()
        
        try:
            all_messages, batch_info = self._collect_batch_messages(messages)
            if not all_messages:
                return
            
            batch_log_prefix = self._build_batch_log_prefix(batch_info)
            logger.info(f"{batch_log_prefix}Processing batch with {len(all_messages)} messages")
            
            processable_messages = await self.consumer_service.get_processable_messages(all_messages)
            if not self._should_process_messages(processable_messages, all_messages, batch_log_prefix):
                await self._commit_safely(all_messages, batch_log_prefix)
                return
            
            results = await self._process_messages_safely(processable_messages, message_processor, batch_log_prefix)
            if results is None:
                return
            
            valid_results, valid_messages = await self._filter_valid_results(results, processable_messages, batch_log_prefix)
            
            if self.produce_topic:
                messages_to_mark = await self.send_results(valid_results, valid_messages, self.produce_topic, batch_log_prefix)
            else:
                messages_to_mark = valid_messages
            
            await self.consumer_service.batch_mark_messages_processed(messages_to_mark)
            await self._commit_safely(all_messages, batch_log_prefix)
            
        except Exception as e:
            logger.error(f"{batch_log_prefix}Error in process_batch_messages: {e}")
    
    def _collect_batch_messages(self, messages: Dict[Any, List[Any]]):
        """收集批次消息并构建批次信息"""
        all_messages: List[Any] = []
        batch_info = ""
        
        for tp, partition_messages in messages.items():
            if not partition_messages:
                continue
            batch_info += f"[{tp.topic}:{tp.partition}|offsets:{partition_messages[0].offset}-{partition_messages[-1].offset}|count:{len(partition_messages)}]; "
            all_messages.extend(partition_messages)
        
        return all_messages, batch_info.strip()
    
    def _should_process_messages(self, processable_messages: List[Any], all_messages: List[Any], log_prefix: str) -> bool:
        """判断是否需要处理消息"""
        all_messages_count = len(all_messages)
        processable_messages_count = len(processable_messages)
        
        if processable_messages_count == 0:
            logger.warning(f"{log_prefix}All {all_messages_count} messages were duplicates, skipping processing.")
            return False
        elif processable_messages_count < all_messages_count:
            logger.info(f"{log_prefix}Found {processable_messages_count} messages to process, {all_messages_count - processable_messages_count} duplicates skipped")
        else:
            logger.info(f"{log_prefix}Processing all {processable_messages_count} messages")
        
        return True
    
    async def _process_messages_safely(self, processable_messages: List[Any], message_processor: Any, log_prefix: str):
        """安全地处理消息并监控关闭信号"""
        results = await self.process_messages_with_shutdown_monitor(
            messages=processable_messages,
            message_processor=message_processor,
            log_prefix=log_prefix
        )
        
        if results is None:
            logger.warning(f"{log_prefix}Message processing cancelled due to shutdown signal")
            return None
        
        return results

    async def _delayed_shutdown(self, shutdown_event: asyncio.Event, timeout: int) -> None:
        """延迟关闭机制"""
        logger.info(f"{self.instance_log_prefix}Waiting {timeout}s for graceful shutdown...")
        try:
            await asyncio.sleep(timeout)
            if not shutdown_event.is_set():
                logger.warning(f"{self.instance_log_prefix}Graceful shutdown timeout {timeout}s reached. Forcing stop.")
                shutdown_event.set()
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Error in delayed shutdown: {e}")
            shutdown_event.set()

    def _register_signal_handlers(self):
        """注册信号处理函数"""
        def handle_signal(signum: int, _: Any) -> None:
            with self._signal_lock:
                if self._signal_received:
                    logger.debug(f"{self.instance_log_prefix}Signal {signum} already handled, ignoring duplicate")
                    return
                
                self._signal_received = True
                self.consumer_service.shutdown_by_signal = True
                logger.info(f"{self.instance_log_prefix}Received signal {signum}, starting graceful shutdown...")
                
                if self.force_shutdown_on_timeout:
                    try:
                        loop = asyncio.get_running_loop()
                        loop.create_task(self._delayed_shutdown(self._shutdown_event, self.graceful_shutdown_timeout))
                        logger.info(f"{self.instance_log_prefix}Force shutdown timeout enabled, timeout: {self.graceful_shutdown_timeout}s")
                    except RuntimeError:
                        logger.warning(f"{self.instance_log_prefix}No running event loop, immediate shutdown")
                        self._shutdown_event.set()
                else:
                    logger.info(f"{self.instance_log_prefix}Graceful shutdown enabled, waiting for current batch to complete")
        
        signal.signal(signal.SIGINT, handle_signal)
        signal.signal(signal.SIGTERM, handle_signal)
        
    async def _cleanup_consumer_loop(self) -> None:
        """清理消费者循环资源"""
        try:
            await self.consumer_service.stop()
            if self.producer_service:
                await self.producer_service.stop()
            await self._cleanup_redis_services()
            logger.info(f"{self.instance_log_prefix}Kafka and Redis resources cleanup completed.")
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Resources cleanup failed: {e}")

    async def start_consumer_loop(self, message_processor: Any) -> None:
        """消费者主循环"""
        self._shutdown_event.clear()
        self._register_signal_handlers()
        
        try:
            await self._initialize_redis_services()
            logger.info(f"{self.instance_log_prefix}Starting consumer loop...")
            await self.consumer_service.start()
            if self.producer_service:
                await self.producer_service.start()
            logger.info(f"{self.instance_log_prefix}Consumer loop started.")

            empty_polls = 0
            
            while not self._signal_received:
                try:
                    if self._shutdown_event.is_set():
                        logger.info(f"{self.instance_log_prefix}Shutdown event set, exiting loop.")
                        break
                    
                    messages = await self.consumer_service.get_messages()
                    if not messages:
                        empty_polls += 1
                        if empty_polls >= self.consumer_service.empty_poll_threshold:
                            # 首先，检查消费者是否还活着
                            if self.consumer_service.alive_check_interval > 0:
                                if not await self.consumer_service.check_liveness():
                                    logger.warning(f"{self.instance_log_prefix}Consumer liveness check failed. Triggering restart.")
                                    # await self.consumer_service.restart()
                                    await asyncio.sleep(5)
                                    continue
                            
                            # 如果消费者是存活的，再执行空闲提交
                            await self.consumer_service.periodic_commit_idle()
                            empty_polls = 0 # 重置计数器
                        
                        await asyncio.sleep(0.1)
                        continue
                    
                    empty_polls = 0
                    await self.process_batch_messages(messages, message_processor, self.produce_topic)
                    
                except (ConnectionError, *CONNECTION_REBUILD_ERRORS) as e:
                    logger.warning(f"{self.instance_log_prefix}A service reported a connection failure and has attempted self-recovery. Error: {e}. Retrying loop after a delay.")
                    await asyncio.sleep(5)
                except Exception as e:
                    logger.error(f"{self.instance_log_prefix}Unhandled exception in consumer loop: {e}", exc_info=True)
                    await asyncio.sleep(20)
            
        except Exception as e:
            logger.error(f"{self.instance_log_prefix}Consumer loop failed to start or terminated unexpectedly: {e}", exc_info=True)
        finally:
            logger.info(f"{self.instance_log_prefix}Consumer loop exited. Shutting down services...")
            await self._cleanup_consumer_loop()