import time
import json
import signal
import asyncio
import functools
from utils.logger import logger
from config.settings import config
from multiprocessing import Process
from aiokafka.errors import (
    KafkaTimeoutError, RequestTimedOutError,
    NotLeaderForPartitionError, LeaderNotAvailableError,
    UnknownTopicOrPartitionError, GroupCoordinatorNotAvailableError,
    NodeNotReadyError, RebalanceInProgressError,
    KafkaConnectionError, NoBrokersAvailable, MetadataEmptyBrokerList,
    ProducerClosed, CommitFailedError,
    TopicAuthorizationFailedError, InvalidTopicError, RecordTooLargeError,
    CorruptRecordException, UnsupportedVersionError, IllegalStateError
)
from services import AI_essay_answer_point, AI_essay_correction
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener
from aiokafka.coordinator.assignors.sticky.sticky_assignor import StickyPartitionAssignor
from services.redis_service import RedisOffsetManager, RedisDeduplicationService, cleanup_all_async_redis_services


# 错误分类定义
RETRYABLE_ERRORS = (
    KafkaTimeoutError,
    RequestTimedOutError,
    NotLeaderForPartitionError,
    LeaderNotAvailableError,
    UnknownTopicOrPartitionError,
    GroupCoordinatorNotAvailableError,
    NodeNotReadyError,
    RebalanceInProgressError,
)

CONNECTION_REBUILD_ERRORS = (
    KafkaConnectionError,
    NoBrokersAvailable,
    MetadataEmptyBrokerList,
    ProducerClosed,
)

NON_RETRYABLE_ERRORS = (
    TopicAuthorizationFailedError,
    InvalidTopicError,
    RecordTooLargeError,
    CorruptRecordException,
    UnsupportedVersionError,
    IllegalStateError,
)


def kafka_error_handler(max_retries=3, retry_interval=1, rebuild_connection=False, raise_on_failure=True):
    """
    Kafka 错误处理装饰器，处理不同类型的 Kafka 错误
    
    Args:
        max_retries: 最大重试次数
        retry_interval: 重试间隔（秒）
        rebuild_connection: 是否需要在重试前重建连接
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(1, max_retries + 1):
                try:
                    return await func(*args, **kwargs)

                except RETRYABLE_ERRORS as e:
                    last_exception = e
                    backoff = retry_interval * (2 ** (attempt - 1))
                    logger.warning(
                        f"[Kafka-Retryable] {func.__name__} failed (attempt {attempt}/{max_retries}): {e}. "
                        f"Retrying in {backoff}s..."
                    )
                    await asyncio.sleep(backoff)

                except CONNECTION_REBUILD_ERRORS as e:
                    last_exception = e
                    backoff = retry_interval * (2 ** (attempt - 1))
                    logger.warning(
                        f"[Kafka-ConnectionError] {func.__name__} failed (attempt {attempt}/{max_retries}): {e}. "
                        f"Rebuilding connection in {backoff}s..."
                    )

                    if rebuild_connection:
                        instance = args[0] if args else None
                        if instance and hasattr(instance, 'rebuild_connection'):
                            try:
                                await instance.rebuild_connection()
                                logger.info(f"[Kafka] Connection rebuilt successfully.")
                            except Exception as rebuild_err:
                                logger.error(f"[Kafka] Failed to rebuild connection: {rebuild_err}")
                    await asyncio.sleep(backoff)

                except NON_RETRYABLE_ERRORS as e:
                    logger.error(f"[Kafka-NonRetryable] {func.__name__} failed: {e}")
                    raise

                except Exception as e:
                    logger.error(f"[Kafka-UnexpectedError] {func.__name__} failed with unexpected error: {e}")
                    raise

            logger.error(f"[Kafka-Failure] {func.__name__} failed after {max_retries} attempts. Last error: {last_exception}")
            if raise_on_failure and last_exception:
                raise last_exception
            return None  # 可选：用于任务失败但不抛异常的情况

        return wrapper
    return decorator


class AIOConsumerRebalanceListener(ConsumerRebalanceListener):
    """处理 rebalance 事件的监听器，将事件委托给回调函数处理"""
    def __init__(self, on_revoke_cb, on_assign_cb):
        self.on_revoke_cb = on_revoke_cb
        self.on_assign_cb = on_assign_cb
    
    async def on_partitions_revoked(self, revoked):
        """分区被撤销时的回调"""
        try:
            if self.on_revoke_cb:
                await self.on_revoke_cb(revoked)
        except Exception as e:
            logger.error(f"Error in on_partitions_revoked callback: {e}")
    
    async def on_partitions_assigned(self, assigned):
        """分区被分配时的回调"""
        try:
            if self.on_assign_cb:
                await self.on_assign_cb(assigned)
        except Exception as e:
            logger.error(f"Error in on_partitions_assigned callback: {e}")


class KafkaConsumerService:
    _consumer = None
    _lock = asyncio.Lock()
    group_id = ""
    topics = []
    last_message_time = None  # 记录最后一次收到消息的时间
    alive_check_interval = 5 * 60 # 检查消费者是否还活着的间隔（秒），默认5分钟
    last_commit_time = None  # 记录最后一次提交时间
    commit_interval = 30 * 60  # 无新消息时定期提交间隔（秒），默认30分钟
    partitions_assigned_delay = 30  # 分区被分配后延迟消费时间(秒)
    running = False
    
    @classmethod
    async def initialize(cls, group_id, topics, batch_size=1, batch_timeout=1.0, use_redis_offset_manager=False, use_redis_deduplication_service=False):
        """初始化消费者"""
        cls.bootstrap_servers = config["KAFKA"]["bootstrap_servers"]
        cls.group_id = group_id
        cls.topics = topics
        cls.offset_reset = config["KAFKA"]["offset_reset"]
        cls.batch_size = batch_size
        cls.batch_timeout = batch_timeout
        cls.use_redis_offset_manager = use_redis_offset_manager
        cls.use_redis_deduplication_service = use_redis_deduplication_service
        cls.last_message_time = time.time()
        cls.last_commit_time  = time.time()
        cls.log_prefix = f"Group_id: {cls.group_id}, Topics: {cls.topics}, "
        
        await cls.create_consumer()
    
    @classmethod
    async def create_consumer(cls):
        """创建消费者实例"""
        async with cls._lock:
            if cls._consumer is not None:
                return
            
            try:
                # 根据开关初始化Redis相关服务
                if cls.use_redis_offset_manager:
                    try:
                        await RedisOffsetManager.initialize()
                        logger.info(f"{cls.log_prefix}RedisOffsetManager enabled and initialized.")
                    except Exception as e:
                        logger.error(f"{cls.log_prefix}Failed to initialize RedisOffsetManager: {e}")

                if cls.use_redis_deduplication_service:
                    try:
                        await RedisDeduplicationService.initialize()
                        logger.info(f"{cls.log_prefix}RedisDeduplicationService enabled and initialized.")
                    except Exception as e:
                        logger.error(f"{cls.log_prefix}Failed to initialize RedisDeduplicationService: {e}")
                        
                cls._consumer = AIOKafkaConsumer(
                    *cls.topics,
                    bootstrap_servers=cls.bootstrap_servers,
                    group_id=cls.group_id, 
                    auto_offset_reset=cls.offset_reset,
                    enable_auto_commit=False, # 不自动提交offset
                    max_poll_records=cls.batch_size, # 每次拉取的最大消息数
                    session_timeout_ms=30 * 1000, # 会话超时时间，30s
                    max_poll_interval_ms=60 * 60 * 1000, # 最大拉取间隔，60分钟
                    heartbeat_interval_ms=5 * 1000, # 心跳间隔，5s
                    partition_assignment_strategy=[StickyPartitionAssignor]
                )
                listener = AIOConsumerRebalanceListener(
                    on_revoke_cb=cls._on_partitions_revoked,
                    on_assign_cb=cls._on_partitions_assigned
                )
                # 注册rebalance回调
                cls._consumer.subscribe(
                    cls.topics,
                    listener=listener
                )

                await cls._consumer.start()
                cls.running = True
                logger.info(f"{cls.log_prefix}Kafka consumer created")
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"{cls.log_prefix}Error creating Kafka consumer: {e}")
                cls._consumer = None
                cls.running = False
            
    @classmethod
    async def stop(cls):
        """停止消费者"""
        async with cls._lock:
            try:
                if cls._consumer:
                    await cls._consumer.stop()
                    logger.info(f"{cls.log_prefix}Closed existing consumer connection")
            except Exception as e:
                logger.error(f"{cls.log_prefix}Error closing existing consumer: {e}")
            finally:
                cls._consumer = None
                cls.running = False
            
            # 清理Redis服务
            if cls.use_redis_offset_manager or cls.use_redis_deduplication_service:
                try:
                    await cleanup_all_async_redis_services()
                    logger.info(f"{cls.log_prefix}Redis services cleaned up")
                except Exception as e:
                    logger.error(f"{cls.log_prefix}Failed to cleanup Redis services: {e}")
    
    @classmethod
    async def rebuild_connection(cls):
        """重建消费者连接"""
        logger.info(f"{cls.log_prefix}Rebuilding Kafka consumer connection...")
        await cls.stop()
        await cls.create_consumer()
        
    @classmethod
    async def _on_partitions_revoked(cls, revoked):
        """分区被撤销时的回调"""
        logger.warning(f"{cls.log_prefix}Partitions revoked: {revoked}")
        
    @classmethod
    async def _on_partitions_assigned(cls, assigned):
        """分区被分配时的回调"""
        try:
            logger.warning(f"{cls.log_prefix}Partitions assigned: {assigned}")

            # 暂停分区，防止 getmany() 提前消费
            cls._consumer.pause(*assigned)

            # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned造成重复消费，所以需要延迟消费
            logger.info(f"{cls.log_prefix}Waiting {cls.partitions_assigned_delay}s after partitions assigned...")
            
            # 不阻塞此回调，立刻返回
            asyncio.create_task(cls._verify_and_set_initial_offsets(assigned))      
        except Exception as e:
            logger.error(f"{cls.log_prefix}Error in _on_partitions_assigned: {e}")

    @classmethod
    async def _verify_and_set_initial_offsets(cls, assigned):
        """验证并设置初始offset，防止从earliest开始消费"""
        try:
            await asyncio.sleep(cls.partitions_assigned_delay)

            # 获取有效分区
            current_assignment = cls._consumer.assignment()
            valid_assignment = [tp for tp in assigned if tp in current_assignment]
            
            if not valid_assignment:
                logger.warning(f"{cls.log_prefix}No valid assignment found, skipping offset verification")
                return
            
            for tp in valid_assignment:
                try:
                    # 构造 offset 列表及其来源
                    sources = []
                    committed_offset = await cls._consumer.committed(tp)
                    if committed_offset is not None:
                        sources.append(("kafka", committed_offset))
                    
                    redis_offset = None
                    if cls.use_redis_offset_manager:
                        redis_offset = await cls._get_offset_from_redis(tp)
                        if redis_offset is not None:
                            sources.append(("redis", redis_offset))

                    # 获取合法 offset 范围
                    earliest = (await cls._consumer.beginning_offsets([tp]))[tp]
                    latest = (await cls._consumer.end_offsets([tp]))[tp]

                    # 过滤有效 offset
                    valid_sources = [(src, offset) for src, offset in sources if earliest <= offset <= latest]

                    if valid_sources:
                        # 选较大的 offset
                        best_src, best_offset = max(valid_sources, key=lambda x: x[1])
                        cls._consumer.seek(tp, best_offset)
                        logger.info(
                            f"{cls.log_prefix}Offset decision for {tp} | "
                            f"selected={best_src}:{best_offset} | "
                            f"earliest={earliest}, latest={latest} | "
                            f"kafka_offset={committed_offset}, redis_offset={redis_offset}, use_redis_offset_manager={cls.use_redis_offset_manager}"
                        )
                    else:
                        # 所有 offset 均无效
                        logger.warning(
                            f"{cls.log_prefix}Offset decision for {tp} | "
                            f"no valid offset found, using auto_offset_reset={cls.offset_reset} | "
                            f"earliest={earliest}, latest={latest} | "
                            f"kafka_offset={committed_offset}, redis_offset={redis_offset}, use_redis_offset_manager={cls.use_redis_offset_manager}"
                        )

                except Exception as e:
                    logger.error(f"{cls.log_prefix}Failed to verify/set offset for {tp}: {e}")

            logger.info(f"{cls.log_prefix}Resuming valid partitions: {valid_assignment}")
            # 恢复分区
            cls._consumer.resume(*valid_assignment)
        except Exception as e:
            logger.error(f"{cls.log_prefix}Error in verify and set initial offsets: {e}")
        
    @classmethod
    async def _get_offset_from_redis(cls, tp):
        """从Redis获取offset备份"""
        try:
            return await RedisOffsetManager.get_offset(cls.group_id, tp.topic, tp.partition)
        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to get offset from Redis for {tp}: {e}")
        return None
    
    @classmethod
    async def mark_message_processed(cls, message):
        """标记消息为已处理"""
        if not cls.use_redis_deduplication_service or not message:
            return
        
        try:
            await RedisDeduplicationService.mark_message_processed(cls.group_id, message)
            logger.debug(f"{cls.log_prefix}Marked message as processed")
        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to mark message as processed: {e}")
    
    @classmethod
    async def batch_mark_messages_processed(cls, messages):
        """批量标记消息为已处理
        
        Args:
            messages: 要标记为已处理的消息列表
        """
        if not cls.use_redis_deduplication_service or not messages:
            return
        
        try:
            await RedisDeduplicationService.batch_mark_messages_processed(cls.group_id, messages)
            logger.debug(f"{cls.log_prefix}Batch marked {len(messages)} messages as processed")
        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to batch mark {len(messages)} messages as processed: {e}")

    @classmethod
    async def _is_consumer_alive(cls):
        """简单检查消费者是否还活着"""
        try:
            if cls._consumer is None:
                return False
            
            # 检查消费者是否已关闭
            if hasattr(cls._consumer, '_closed') and cls._consumer._closed:
                return False
            
            # 检查是否长时间没有收到消息（超过5分钟认为可能有问题）
            if cls.last_message_time and (time.time() - cls.last_message_time) > cls.alive_check_interval:
                # 尝试获取分区信息，这会触发与broker的通信
                try:
                    assigned_partitions = cls._consumer.assignment()
                    if not assigned_partitions:
                        logger.warning(f"{cls.log_prefix}Consumer has no assigned partitions. "
                                    f"This may be normal if the number of consumers exceeds the number of partitions.")
                        return True
                    # 简单测试：尝试获取一个分区的位置
                    for tp in list(assigned_partitions)[:1]:
                        await asyncio.wait_for(cls._consumer.position(tp), timeout=5.0)
                        break
                except (asyncio.TimeoutError, Exception):
                    return False
            
            return True
        except Exception:
            return False
        
    @classmethod
    async def periodic_commit_idle(cls):
        """拉取消息为空时定期提交偏移量"""
        try:
            now = time.time()
            if now - cls.last_commit_time > cls.commit_interval:
                try:
                    if cls._consumer.assignment():
                        await cls._consumer.commit()
                    cls.last_commit_time = now
                    # logger.debug(f"{cls.log_prefix}periodic commit successful (idle >{cls.commit_interval}s)")
                except Exception as ce:
                    logger.warning(f"{cls.log_prefix}periodic commit failed: {ce}")

        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to periodic commit: {e}")
        
    @classmethod
    @kafka_error_handler(rebuild_connection=True, raise_on_failure=False)
    async def consume_messages(cls):
        try:
            # 批量拉取消息
            messages = await cls._consumer.getmany(timeout_ms=int(cls.batch_timeout * 1000), max_records=cls.batch_size)

            # 如果收到消息，更新最后消息时间
            if messages:
                cls.last_message_time = time.time()
                
            return messages
        except Exception as e:
            raise
    
    @classmethod
    async def get_processable_messages(cls, messages):
        """获取需要处理的消息"""
        if not cls.use_redis_deduplication_service or not messages:
            return messages
        
        try:
            # 批量检查消息是否已处理
            batch_status = await RedisDeduplicationService.batch_check_messages(cls.group_id, messages)
            
            processable_messages = []
            for message in messages:
                message_id = f"{cls.group_id}:{message.topic}:{message.partition}:{message.offset}"
                if not batch_status.get(message_id, False):
                    processable_messages.append(message)

            return processable_messages
        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to get processable messages: {e}")
            return messages
        
    @classmethod
    async def _get_max_offsets(cls, messages):
        """获取每个分区的最大offset"""
        partitions = {}
        for message in messages:
            tp = TopicPartition(message.topic, message.partition)
            if tp not in partitions or message.offset > partitions[tp]:
                partitions[tp] = message.offset
        return partitions
    
    @classmethod
    @kafka_error_handler(rebuild_connection=True)
    async def commit_partition_offset(cls, tp, offset):
        """提交单个分区的偏移量"""
        try:
            # 提交下一个偏移量
            await cls._consumer.commit({tp: offset + 1})
            # 更新最近一次提交时间
            cls.last_commit_time = time.time()

            # 获取提交后的实际偏移量
            committed_offset = await cls._consumer.committed(tp)
            logger.info(f"{cls.log_prefix}Committed successfully, tp: {tp}, offset: {offset}, Committed offset: {committed_offset}")
        except CommitFailedError as cfe:
            # CommitFailedError 通常是由于 rebalance 导致，分区可能已经分配给其他消费者，此时不应再提交
            logger.error(f"{cls.log_prefix}Commit failed, tp: {tp}, offset: {offset}, error: {cfe}")
        except Exception as e:
            raise

    @classmethod
    async def _save_offsets_to_redis(cls, offsets):
        """保存offset到Redis - 性能优化"""
        if not offsets:
            return
        
        try:
            # 使用 Lua 脚本批量保存偏移量
            await RedisOffsetManager.batch_save_offsets_lua(cls.group_id, offsets)
            logger.info(f"{cls.log_prefix}Successfully saved {len(offsets)} offsets to Redis, offsets: {offsets}")
        except Exception as e:
            logger.error(f"{cls.log_prefix}Failed to save offsets to Redis, offsets: {offsets}, error: {e}")
        
    @classmethod
    async def commit_offsets(cls, messages):
        """提交偏移量"""
        if not messages:
            return
        
        # 取出每个分区的最大偏移量
        max_offsets = await cls._get_max_offsets(messages)
        
        # 批量保存偏移量到Redis
        if cls.use_redis_offset_manager:
            await cls._save_offsets_to_redis(max_offsets)
        
        # 为每个分区提交最大偏移量
        for tp, offset in max_offsets.items():
            try:
                await cls.commit_partition_offset(tp, offset)
            except Exception as e:
                logger.error(f"{cls.log_prefix}Error committing offset for {tp}, offset: {offset}, error: {e}")


class KafkaProducerService:
    _producer = None
    _lock = asyncio.Lock()
    
    @classmethod
    async def initialize(cls):
        """创建Kafka生产者"""
        await cls.create_producer()
    
    @classmethod
    async def create_producer(cls):
        """创建生产者实例"""
        async with cls._lock:
            if cls._producer is not None:
                return
            
            producer = AIOKafkaProducer(
                bootstrap_servers=config.get("KAFKA").get('bootstrap_servers'),
                acks='all', # 所有副本都确认消息发送成功
                # enable_idempotence=True,  # 启用幂等性
                compression_type='gzip', # 压缩类型
                value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8') if isinstance(v, (dict, list)) else str(v).encode('utf-8'), # 序列化器
                max_batch_size=512 * 1024, # 最大批次大小
                linger_ms=20,  # 等待时间，允许更多消息加入批次
            )
            await producer.start()
            
            cls._producer = producer
            logger.info("Kafka producer initialized")
    
    @classmethod
    async def stop(cls):
        """停止producer连接"""
        async with cls._lock:
            try:
                if cls._producer is not None:
                    await cls._producer.stop()
                    logger.info(f"Producer closed")
            except Exception as e:
                logger.error(f"Failed to close producer: {e}")
            finally:
                cls._producer = None
        
    @classmethod
    async def rebuild_connection(cls):
        """重建生产者连接"""
        await cls.stop()
        await cls.create_producer()
        
    @classmethod
    @kafka_error_handler(rebuild_connection=True)
    async def send_message(cls, topic, message):
        """发送消息到指定topic"""
        if cls._producer is None:
            await cls.initialize()
        
        await cls._producer.send(topic, value=message)
    
    @classmethod
    @kafka_error_handler(rebuild_connection=True)
    async def batch_send_messages(cls, topic, messages):
        """批量发送消息到指定topic"""
        if cls._producer is None:
            await cls.initialize()
        
        # 准备发送任务
        send_tasks = [await cls._producer.send(topic, value=message) for message in messages]
        
        # 并行发送所有消息
        results = await asyncio.gather(*send_tasks, return_exceptions=True)
        return results


class KafkaService:
    @classmethod
    def _signal_handler(cls, signum, frame):
        """处理系统信号"""
        logger.info(f"Received signal {signum}, shutting down...")
        cls.shutdown()

    @classmethod
    def shutdown(cls):
        """优雅关闭所有资源"""
        logger.info("Shutting down Kafka services...")
        KafkaConsumerService.running = False
    
    @staticmethod
    def _get_log_prefix(message):
        """获取消息日志前缀"""
        try:
            log_prefix = f"Topic: {message.topic}, Partition: {message.partition}, Offset: {message.offset}, "
            return log_prefix
        except Exception as e:
            logger.error(f"Failed to get log_prefix for {message}, error: {e}")
            return ""
    
    @staticmethod
    def _enrich_log_prefix(log_prefix, data):
        """根据消息数据丰富日志前缀"""
        try:
            if "answerId" in data:
                log_prefix += f"answerId: {data.get('answerId')}, "
            elif "questionId" in data:
                log_prefix += f"questionId: {data.get('questionId')}, "
            return log_prefix
        except Exception as e:
            logger.error(f"Failed to enrich log prefix for {data}, error: {e}")
            return log_prefix
            
    @classmethod
    async def _process_message(cls, message, message_processor):
        """处理单条消息"""
        log_prefix = cls._get_log_prefix(message)
        try:
            data = json.loads(message.value.decode('utf-8'))
            log_prefix = cls._enrich_log_prefix(log_prefix, data)
            
            # 记录消息信息
            logger.info(f"{log_prefix}start processing message")
            # 处理消息
            result, cost_time = await message_processor(data)

            logger.info(f"{log_prefix}processed message successfully, cost time: {cost_time}s")
            return result
        except Exception as e:
            logger.error(f"{log_prefix}failed to process message, message: {message.value.decode('utf-8')}, error: {e}")
            return None

    @classmethod
    async def process_message_batch(cls, messages, message_processor):
        """批量处理消息"""
        if not messages:
            return []
            
        # 使用asyncio.gather并发处理消息
        tasks = [
            cls._process_message(message, message_processor)
            for message in messages
        ]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    @classmethod
    async def _process_send_results(cls, batch_results, messages, produce_topic):
        """处理发送结果"""
        successful_send_messages = []
        for send_result, message in zip(batch_results, messages):
            try:
                log_prefix = cls._get_log_prefix(message)
                if isinstance(send_result, Exception):
                    logger.error(f"{log_prefix}Failed to send message to {produce_topic}, error: {send_result}")
                else:
                    successful_send_messages.append(message)
            except Exception as e:
                logger.error(f"Failed to process send results, result: {send_result}, message: {message}, error: {e}")
        
        return successful_send_messages
        
    @classmethod
    async def produce_results(cls, results, messages, produce_topic, log_prefix=""):
        """批量发送处理结果到目标topic"""
        try:
            # 批量发送所有有效结果
            batch_results = await KafkaProducerService.batch_send_messages(
                topic=produce_topic,
                messages=results
            )

            # 处理发送结果
            successful_send_messages = await cls._process_send_results(batch_results, messages, produce_topic)
            if len(successful_send_messages) == len(results):
                logger.info(f"{log_prefix}All {len(results)} messages were successfully sent to {produce_topic}")
            else:
                logger.error(f"{log_prefix}Successful send {len(successful_send_messages)} messages out of {len(results)} messages to {produce_topic}")
            
            return successful_send_messages
        except Exception as e:
            logger.error(f"{log_prefix}Failed to send messages to {produce_topic}, error: {e}")
            return []

    @classmethod
    async def _filter_valid_results(cls, results, messages, log_prefix=""):
        """过滤有效的处理结果和对应消息"""
        valid_results = []
        valid_messages = []
        
        for result, message in zip(results, messages):
            try:
                current_log_prefix = cls._get_log_prefix(message)
                
                # 检查结果是否有效
                if isinstance(result, Exception):
                    logger.error(f"{current_log_prefix}Task failed with error: {result}, Message: {message.value.decode('utf-8')}")
                    continue
                elif result is None:
                    logger.warning(f"{current_log_prefix}No result from processor, Message: {message.value.decode('utf-8')}")
                    continue
                    
                # 收集有效结果和对应消息
                valid_results.append(result)
                valid_messages.append(message)
            except Exception as e:
                logger.error(f"{log_prefix}Failed to filter valid results: {e}")
            
        return valid_results, valid_messages
    
    @classmethod
    async def _commit_safely(cls, messages, log_prefix=""):
        """安全提交偏移量"""
        try:
            await KafkaConsumerService.commit_offsets(messages)
        except Exception as e:
            logger.error(f"{log_prefix}Failed to commit offsets: {e}")
        
    @classmethod
    async def process_once(cls, messages, message_processor, produce_topic):
        """批量拉取消息并处理"""
        log_prefix = f"Consuming messages - " # 日志前缀
        all_messages = [] # 所有消息
        try:
            # 处理每个分区的消息
            for tp, partition_messages in messages.items():
                if not partition_messages:
                    continue
                
                log_prefix += f"Topic: {tp.topic}, Partition: {tp.partition}, Offsets: {partition_messages[0].offset} to {partition_messages[-1].offset}, Count: {len(partition_messages)}; "                
                all_messages.extend(partition_messages)
            logger.info(log_prefix)

            # 获取需要处理的消息
            processable_messages = await KafkaConsumerService.get_processable_messages(all_messages)
            if not processable_messages:
                logger.warning(f"{log_prefix}All {len(all_messages)} messages were duplicates, skipping processing.")
                return
            logger.info(f"{log_prefix}Found {len(processable_messages)} messages to process, {len(all_messages)} messages in total.")

            # 异步批量处理消息
            results = await cls.process_message_batch(messages=processable_messages, message_processor=message_processor)

            # 处理有效结果和对应消息
            valid_results, valid_messages = await cls._filter_valid_results(results, processable_messages, log_prefix)
            if len(valid_results) == len(processable_messages):
                logger.info(f"{log_prefix}All {len(processable_messages)} messages were successfully processed.")
            elif len(valid_results) > 0:
                logger.error(f"{log_prefix}Successful process {len(valid_results)} messages out of {len(processable_messages)} messages.")
            else:
                logger.error(f"{log_prefix}No valid results after processing {len(processable_messages)} messages.")
                return
            
            # 如果需要发送到其他topic
            if produce_topic:
                successful_send_messages = await cls.produce_results(valid_results, valid_messages, produce_topic, log_prefix)
                # 批量标记发送成功的消息已处理
                if successful_send_messages:
                    await KafkaConsumerService.batch_mark_messages_processed(successful_send_messages)
            else:
                # 批量标记处理成功的消息已处理
                await KafkaConsumerService.batch_mark_messages_processed(valid_messages)
            
        except Exception as e:
            logger.error(f"{log_prefix}Error in consuming and processing messages: {e}")
        finally:
            if all_messages:
                await cls._commit_safely(all_messages, log_prefix)

    @classmethod
    def _register_signal_handlers(cls):
        signal.signal(signal.SIGINT, cls._signal_handler)
        signal.signal(signal.SIGTERM, cls._signal_handler)
    
    @classmethod
    async def start_consumer_loop(cls, group_id, topics, message_processor, produce_topic=None, batch_size=1, use_redis_offset_manager=False, use_redis_deduplication_service=False):
        """
        消费者循环

        Args:
            group_id: 消费者组ID
            topics: 消费的主题列表
            message_processor: 消息处理函数
            produce_topic: 处理后发送的目标主题(可选)
            batch_size: 批处理大小
            use_redis_offset_manager: 是否使用Redis偏移量管理器
            use_redis_deduplication_service: 是否使用Redis消息去重服务
        """
        try:
            empty_count = 0  # 连续空消息计数
            cls._register_signal_handlers()
            
            # 初始化消费者
            await KafkaConsumerService.initialize(
                group_id=group_id,
                topics=topics,
                batch_size=batch_size,
                use_redis_offset_manager=use_redis_offset_manager,
                use_redis_deduplication_service=use_redis_deduplication_service
            )

            # 如果需要发送消息到其他topic，则初始化生产者
            if produce_topic:
                await KafkaProducerService.initialize()
            
            # 开始消费消息
            while KafkaConsumerService.running:
                # 批量拉取消息
                messages = await KafkaConsumerService.consume_messages()
                if not messages:
                    empty_count += 1
                    # 连续10次空消息后检查连接健康状态，提交偏移量
                    if empty_count >= 10:
                        if not await KafkaConsumerService._is_consumer_alive():
                            logger.warning(f"Group_id: {group_id}, Topics: {topics}, Consumer connection is abnormal, trying to rebuild connection...")
                            await KafkaConsumerService.rebuild_connection()
                        
                        await KafkaConsumerService.periodic_commit_idle()
                        empty_count = 0  # 重置计数
                    await asyncio.sleep(0.1)  # 如果消息为空，则等待0.1秒
                    continue
                
                await cls.process_once(messages, message_processor, produce_topic)
            
            logger.info(f"Group_id: {group_id}, Topics: {topics}, Consumer loop exited")
        except Exception as e:
            logger.error(f"Group_id: {group_id}, Topics: {topics}, Failed in consumer loop: {e}")
        finally:
            await KafkaConsumerService.stop()
            if produce_topic:
                await KafkaProducerService.stop()
        
    @classmethod
    def start_consuming(cls):
        """启动消费进程"""
        group_id = config["KAFKA"]["group_id"]
        topic = [config["KAFKA"]["insp_data_topic"]]
        produce_topic = config["KAFKA"]["insp_data_result_topic"]
        batch_size = int(config["KAFKA"].get("batch_size", 1))
        use_redis_offset_manager = config["KAFKA"].get("use_redis_offset_manager", False) is True
        use_redis_deduplication_service = config["KAFKA"].get("use_redis_deduplication_service", False) is True
        try:
            asyncio.run(cls.start_consumer_loop(
                group_id,
                topic,
                AI_essay_correction.run,
                produce_topic=produce_topic,
                batch_size=batch_size,
                use_redis_offset_manager=use_redis_offset_manager,
                use_redis_deduplication_service=use_redis_deduplication_service
            ))
        except Exception as e:
            logger.error(f"Failed in start consuming: {e}")
    
    @classmethod
    def start_answer_point_consuming(cls):
        """启动消费进程"""
        group_id = config["KAFKA"]["point_group_id"]
        topic = [config["KAFKA"]["insp_answer_point_topic"]]
        produce_topic = config["KAFKA"]["insp_answer_point_result_topic"]
        batch_size = int(config["KAFKA"].get("batch_size", 1))
        use_redis_offset_manager = config["KAFKA"].get("use_redis_offset_manager", False) is True
        use_redis_deduplication_service = config["KAFKA"].get("use_redis_deduplication_service", False) is True
        try:
            asyncio.run(cls.start_consumer_loop(
                group_id,
                topic,
                AI_essay_answer_point.run,
                produce_topic=produce_topic,
                batch_size=batch_size,
                use_redis_offset_manager=use_redis_offset_manager,
                use_redis_deduplication_service=use_redis_deduplication_service
            ))
        except Exception as e:
            logger.error(f"Failed in start answer point consuming: {e}")

    @classmethod
    def start_processes(cls, max_threshold=1):
        """启动多个消费者进程"""
        processes = []
        
        for _ in range(max_threshold):
            p = Process(target=cls.start_consuming)
            p.start()
            processes.append(p)
        
        for _ in range(2):
            p = Process(target=cls.start_answer_point_consuming)
            p.start()
            processes.append(p)
        
        for p in processes:
            p.join()
