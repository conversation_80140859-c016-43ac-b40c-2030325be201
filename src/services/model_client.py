import json
import ujson
import asyncio
from services.prompt import *
from datetime import datetime
from utils.score_tools import *
from config.settings import config
from services.ai_service import AIService
from services.analysis_manager import AnalysisManager
from utils.general_tools import extract_json_from_markdown, get_use_tokens


class ModelClient:
    """
    模型处理类
    """

    def __init__(self, log_prefix=""):
        self.aisuite_config = config.get("AISuite", {})
        self.log_prefix = log_prefix

    def select_model(self, tokens=0, model_type=None):
        """选择模型列表"""
        # 卷面分析(豆包)
        if model_type == "article_appearance_doubao":
            return ['doubao_thinking_vision_pro']
        # 卷面分析(千问)
        elif model_type == "article_appearance_qwen":
            return ['qwen_vl_max_latest']
        # 文章分级、标题扣分、文采加分
        elif model_type == "article_grading":
            return ['doubao_thinking_pro', 'doubao_deepseek_r1_250120', 'qwen_3_235b_a22b']
        # 文章写作点评
        elif model_type == "article_review":
            if tokens >= 20 * 1024:
                return ['doubao_256k_search', 'qwen_plus', 'qwen_long']
            return ['doubao_32k_search', 'qwen_max']
        # 逐句点评
        elif model_type == "sentence_review":
            if tokens >= 20 * 1024:
                return ['doubao_256k_search', 'qwen_plus', 'qwen_long']
            return ['doubao_32k_search', 'qwen_max']
        # 答案要点生成
        elif model_type == "answer_point":
            return ['doubao_seed_1.6_thinking', 'qwen_3_235b_a22b', 'doubao_deepseek_r1_250528']
        else:
            if tokens >= 20 * 1024:
                return ['doubao_256k', 'qwen_plus', 'openai', 'qwen_long']
            return ['doubao_32k', 'qwen_max', 'openai']

    async def from_model_get_result(self, model_name, chat_cls, data, step_num):
        """
        由于返回内容不确定是哪个模型，所以需要按照模型区分结构
        目前要点批改有两个情况需要重试
        ① 由于我们设置返回结果必须是json格式，如果不是json格式，
        ② 如果模型返回的要点批改数量不等于后台的要点批改的数量，那么需要重新返回
        申论批改
            非文章写作 step 2 要点划线批改 3 逻辑批改 4得分计算  7 总体点评 8 回答要点再处理
            文章写作 step 11 标题提取 12 卷面分析 13 文章分级、标题扣分、文采加分 15 逐句点评 16 语言表达点评
        要点生成
            step 21 生成答案要点 22 答案要点应用文格式处理 23二次校正答案要点
        """
        result = False
        input_tokens = 0
        output_tokens = 0
        
        # 流式输出
        if "async_generator" in str(type(chat_cls)):
            chat_cls_dict = chat_cls
            json_data = ""
            async for chunk in chat_cls:
                input_tokens, output_tokens = await get_use_tokens(chunk)
                if "ChatCompletionChunk" in str(type(chunk)):
                    choices = json.loads(chunk.model_dump_json())['choices']
                    if choices == [] or choices[0]['delta']['content'] is None:
                        continue
                    content = choices[0]['delta']['content']
                    json_data += content
                elif 'result' in chunk:  # qianfan Method
                    content = chunk['result']
                    json_data += content
                else:
                    json_data += chunk
        # 如果是ChatCompletion类型，则调用to_dict方法
        elif "ChatCompletion" in str(type(chat_cls)):
            chat_cls_dict = chat_cls.to_dict()
            json_data = chat_cls_dict["choices"][0]['message']['content']
            input_tokens, output_tokens = await get_use_tokens(chat_cls)
        # dashscope 类型
        elif "dashscope" in str(type(chat_cls)):
            chat_cls_dict = chat_cls
            json_data = chat_cls_dict["output"]["choices"][0]['message']['content'][0]['text']
            input_tokens, output_tokens = await get_use_tokens(chat_cls)
        else:
            raise Exception(f"{self.log_prefix}unexcepted chat_cls type={type(chat_cls)}, chat_cls={chat_cls}")

        try:
            logger.info(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, round_parse result={chat_cls_dict}.")
            result = json.loads(extract_json_from_markdown(json_data))
        except json.decoder.JSONDecodeError as json_ee:
            result = ujson.loads(extract_json_from_markdown(json_data))
        except Exception as ee:
            raise Exception(f"{self.log_prefix}from_model_get_result error:{ee}")
        
        # 要点划线批改
        if step_num == 2:
            # 应用文返回格式与其它不一样，需要单独处理
            if data['questionType'] == 4:
                key_point_results = result["回答要点批改结果"]
            else:
                key_point_results = result
            # 判断要点点评数量是否正确
            if len(data['keyPoint']['questionKeyPoints']) != len(key_point_results):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 要点点评数量不正确, 后台要点点评数量={len(data['keyPoint']['questionKeyPoints'])}, 模型返回要点点评数量={len(key_point_results)}")
                return False, input_tokens, output_tokens
            
            # 判断返回结果中，要点评分是否能全部转为数值型，keyPointId是否存在
            for answer_point_result in key_point_results:
                if "keyPointId" not in answer_point_result:
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 要点点评结果中没有keyPointId: {answer_point_result}")
                    return False, input_tokens, output_tokens
                if not isinstance(get_clear_score(answer_point_result.get("要点得分")), float):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 要点点评结果中要点得分格式错误: {answer_point_result.get('要点得分')}")
                    return False, input_tokens, output_tokens
                if not isinstance(answer_point_result.get("回答要点分类"), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 要点点评结果中回答要点分类不是字符串: {answer_point_result.get('回答要点分类')}")
                    return False, input_tokens, output_tokens
        # 逻辑批改
        elif step_num == 3:
            # 应用文返回格式与其它不一样
            if data['questionType'] == 4:
                str_keys = ["逻辑层次扣分", "逻辑层次点评"]
            else:
                str_keys = ["逻辑条理扣分", "逻辑条理点评"]
            for key in str_keys:
                if not isinstance(result.get(key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逻辑批改结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
        # 点评结果格式检查
        elif step_num == 7:
            str_keys = ["审题认知点评", "语言表达点评", "整体点评"]
            for key in str_keys:
                if not isinstance(result.get(key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 点评结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
        # 标题提取
        elif step_num == 11:
            key = "标题"
            if not isinstance(result.get(key), str):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 标题提取的结果格式错误, key={key}, value={result.get(key)}")
                return False, input_tokens, output_tokens
        # 卷面分析
        elif step_num == 12:
            str_keys = ["卷面等级", "卷面美观度", "卷面美观度评级原因", "卷面点评"]
            for key in str_keys:
                if not isinstance(result.get(key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 卷面分析的结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
            key = "卷面等级"
            if result[key] not in ["卷面整洁", "卷面基本整洁", "卷面不够整洁", "卷面比较凌乱"]:
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 卷面分析的结果不在预设值中, key={key}, value={result[key]}")
                return False, input_tokens, output_tokens
            key = "卷面美观度"
            if result[key] not in ["优", "非优"]:
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 卷面分析的结果不在预设值中, key={key}, value={result[key]}")
                return False, input_tokens, output_tokens
        # 文章分级、标题扣分、文采加分
        elif step_num == 13:
            # 判断文章内容等级是否在1-4之间
            key = "内容等级"
            if not result.get(key) in [1, 2, 3, 4]:
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 文章分级、标题扣分、文采加分结果不在预设值中, key={key}, value={result.get(key)}")
                return False, input_tokens, output_tokens
            score_keys = ["标题缺失扣分", "标题不符中心论点扣分", "标题与正文不符扣分", "文采加分"]
            for key in score_keys:
                if not isinstance(get_clear_score(result.get(key)), float):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 文章分级、标题扣分、文采加分结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
            str_keys = ["内容等级原因"] 
            for key in str_keys:
                if not isinstance(result.get(key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 文章分级、标题扣分、文采加分结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
        # 逐句点评
        elif step_num == 15:
            key = "分句点评结果"
            sentence_review_result = result.get(key)
            if not isinstance(sentence_review_result, list):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逐句点评结果格式错误, key={key}, value={sentence_review_result}")
                return False, input_tokens, output_tokens
            for item in sentence_review_result:
                if not isinstance(item, dict):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逐句点评的分句点评的元素格式错误, value={item}")
                    return False, input_tokens, output_tokens
            id_key = "分句ID"
            str_keys = ["分句内容", "表达等级", "点评", "润色示例"]
            reviews_ids = []
            for item in sentence_review_result:
                for key in str_keys:
                    if not isinstance(item.get(key), str):
                        logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逐句点评的分句点评的元素格式错误, key={key}, value={item.get(key)}")
                        return False, input_tokens, output_tokens
                if not isinstance(item.get(id_key), int):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逐句点评的分句点评的元素格式错误, key={id_key}, value={item.get(id_key)}")
                    return False, input_tokens, output_tokens
                reviews_ids.append(item.get(id_key))
            if set(reviews_ids) != set(range(1, len(data["keyPoint"]["group"]) + 1)):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 逐句点评结果分句ID不全。")
                return False, input_tokens, output_tokens
        # 语言表达点评
        elif step_num == 16:
            str_keys = ["语言表达点评", "审题立意点评", "论据点评", "逻辑条理点评"]
            for key in str_keys:
                if not isinstance(result.get(key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 语言表达点评结果格式错误, key={key}, value={result.get(key)}")
                    return False, input_tokens, output_tokens
            key = "写作建议"
            advice_key = "建议"
            urgent_key = "紧急程度"
            if not isinstance(result.get(key), list):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 语言表达点评结果格式错误, key={key}, value={result.get(key)}")
                return False, input_tokens, output_tokens
            for item in result.get(key):
                if not isinstance(item, dict):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 语言表达点评结果的写作建议的元素格式错误, value={item}")
                    return False, input_tokens, output_tokens
                if not isinstance(item.get(advice_key), str):
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 语言表达点评结果的写作建议的元素格式错误, key={advice_key}, value={item.get(advice_key)}")
                    return False, input_tokens, output_tokens
                if item.get(urgent_key) not in ["1", "2", "3"]:
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 语言表达点评结果的写作建议的元素格式错误, key={urgent_key}, value={item.get(urgent_key)}")
                    return False, input_tokens, output_tokens
        # 答案要点应用文格式处理
        elif step_num == 22:
            key = "正文"
            if not isinstance(result.get(key), str):
                logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点生成应用文格式处理的结果格式错误, key={key}, value={result.get(key)}")
                return False, input_tokens, output_tokens
        # 答案要点生成、二次校正答案要点
        elif step_num in [21, 23]:
            # 如果要点有单条超过200字符、分数不是0.5的倍数、AI请求失败：切换模型重新生成
            expected_score = data["keyPoint"]["score"]
            answer_point_key = "答案要点"
            point_key = "要点"
            score_key = "要点分数"
            point_score = 0
            if not isinstance(result.get(answer_point_key), list):
                raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段不是列表")
            
            for item in result.get(answer_point_key):
                if not isinstance(item, dict):
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素不是字典")
                point = item.get(point_key)
                if not isinstance(point, str):
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素的要点不是字符串")
                if len(point) > 200:
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素要点超过200字符")
                score = item.get(score_key)
                if not isinstance(score, (float, int)):
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素得分不是浮点数或整数")
                if score <= 0:
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素得分小于等于0")
                if score % 0.5 != 0:
                    raise Exception(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 答案要点字段的元素得分不是0.5的倍数")
                point_score += score

            # 二次校正答案要点, 如果答案要点的“要点分数”加和仍然不等于总分，在当前模型请求3次
            if step_num == 23:
                if point_score != expected_score:
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, 二次校正的答案要点字段的元素得分与总分不一致, 后台总分={expected_score}, 模型返回总分={point_score}")
                    return False, input_tokens, output_tokens
        
        return result, input_tokens, output_tokens

    async def round_parse(self, data, tokens, prompt, step_num=2, model_type=None):
        """
        根据 token 长度选择模型并发起请求，优先使用可用模型
        Args:
            data: 输入数据
            tokens (int): token长度
            prompt (str): 提示词
        Returns:
            tuple: (解析结果, 模型名称)
        """
        model_list = self.select_model(tokens, model_type)
        aisuite_enabled = self.aisuite_config.get("enable_aisuite", False)
        start_time = datetime.now()
        for model_name in model_list:
            stream = False
            # 重尝试3次
            for retry_num in range(3):
                try:
                    # Qwen3 开源版模型默认开启思考模式，如需关闭，请设置extra_body={"enable_thinking": False}
                    # Qwen3 开源模型在思考模式下不支持非流式输出方式。
                    if "qwen_3_235b_a22b" in model_name:
                        stream = True
                    
                    # 针对qwen_vl_max系列模型，vl_high_resolution_images参数仅支持DashScope Python SDK及HTTP方式下使用
                    if "qwen_vl_max" in model_name:
                        response = await AIService.dashscope(
                            model_name, prompt,
                            stream=stream,
                            vl_high_resolution_images=True, # 高分辨率图像理解
                            custom_resp="chatCompletion"
                        )
                    elif aisuite_enabled:
                        response = await AIService.aisuite(
                            "empty", prompt=prompt,
                            stream=stream,
                            model_name=model_name,
                            environment=config["Environment"]["env"],
                        )
                    else:
                        primary_model = model_name.split("_")[0]
                        response = await getattr(AIService, primary_model)(
                            model_name, prompt, stream=stream, custom_resp="chatCompletion"
                        )
                    result, prompt_tokens, completion_tokens = await self.from_model_get_result(model_name, response, data, step_num)
                    # 输出格式问题，针对当前模型重试
                    if result == False:
                        logger.error(f"{self.log_prefix}retry_num: {retry_num + 1}, model {model_name} failed")
                        continue
                    # 记录成功日志
                    AnalysisManager.save_correction_log(data, prompt, result, model_name, starttime=start_time, endtime=datetime.now(), step_num=step_num,
                                                        prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
                    return result
                except Exception as error:
                    logger.error(f"{self.log_prefix}model_name={model_name}, step_num={step_num}, round_parse error:{error}", exc_info=True)
                    # 非格式问题，切换模型
                    break

        return False

    async def get_line_logical_results(self, model_cls, data, tokens, line_prompt, logical_prompt):
        # 并发获取两个prompt的结果
        line_logical_results = await asyncio.gather(
            model_cls.round_parse(data, tokens, line_prompt, 2),
            model_cls.round_parse(data, tokens, logical_prompt, 3)
        )
        if not line_logical_results[0]:
            raise Exception("获取要点划线批改结果失败")
        if not line_logical_results[1]:
            raise Exception("获取逻辑点评结果失败")
        return line_logical_results

    async def get_final_prompt_and_result(self, model_cls, data, tokens, format_review, logical_result, key_point_review, comment_conclusion):
        # 获取最终点评Prompt
        last_prompt = await get_last_prompt(data, format_review, logical_result, key_point_review, comment_conclusion)
        if not last_prompt:
            raise Exception("最终点评模块获取prompt模板失败")
        # 获取最终点评结果
        last_result = await asyncio.gather(model_cls.round_parse(data, tokens, last_prompt, 7))
        if not last_result:
            raise Exception("获取最终点评结果失败")
        return last_result

    async def get_article_appearance_result(self, model_cls, data, final_url):
        """
        卷面分析
        豆包模型调用使用 openai 兼容模式
        千问模型调用使用 dashscope
        两种方式的 prompt 格式不同，模型传参不同
        """
        try:
            # 豆包模型
            appearance_prompt = await get_article_appearance_prompt(data, final_url)
            if appearance_prompt is None:
                raise Exception("获取卷面分析豆包模型prompt失败")
            appearance_result = await model_cls.round_parse(data, 0, appearance_prompt, step_num=12, model_type="article_appearance_doubao")
            if appearance_result:
                return appearance_result
            
            logger.warning(f"{self.log_prefix}豆包模型获取卷面分析结果失败, 尝试千问模型。")
            
            # 千问模型
            appearance_prompt = await get_article_appearance_prompt(data, final_url, is_dashscope=True)
            if appearance_prompt is None:
                raise Exception("获取卷面分析千问模型prompt失败")
            appearance_result = await model_cls.round_parse(data, 0, appearance_prompt, step_num=12, model_type="article_appearance_qwen")
            if not appearance_result:
                raise Exception("获取卷面分析结果失败")
            return appearance_result
        except Exception as e:
            logger.error(f"{self.log_prefix}获取卷面分析结果失败: {e}")
            return None
    
    async def get_article_grading_result(self, model_cls, data):
        # 文章分级、标题扣分、文采加分
        grading_prompt = await get_article_grading_prompt(data)
        if grading_prompt is None:
            raise Exception("获取文章分级、标题扣分、文采加分prompt失败")
        grading_result = await model_cls.round_parse(data, 0, grading_prompt, step_num=13, model_type="article_grading")
        if not grading_result:
            raise Exception("获取文章分级、标题扣分、文采加分结果失败")
        return grading_result

    async def get_sentence_review_result(self, model_cls, data, tokens, sentence_review_prompt):
        sentence_review_result = await model_cls.round_parse_search(data, tokens, sentence_review_prompt, step_num=15, model_type="sentence_review")
        if not sentence_review_result:
            raise Exception("获取逐句点评结果失败")
        return sentence_review_result

    async def get_article_overall_review_result(self, model_cls, data, tokens, appearance_result, score_result):
        article_review_prompt = await get_article_overall_review_prompt(data, appearance_result, score_result)
        if article_review_prompt is None:
            raise Exception("获取语言表达点评、审题立意点评、论据点评、逻辑条理点评、写作建议prompt失败")
        article_review_result = await model_cls.round_parse_search(data, tokens, article_review_prompt, step_num=16, model_type="article_review")
        if not article_review_result:
            raise Exception("获取语言表达点评结果失败")
        return article_review_result

    async def round_parse_search(self, data, tokens, prompt, step_num=15, model_type=None):
        """
        启用联网插件，根据 token 长度选择模型并发起请求，优先使用可用模型
        """
        model_list = self.select_model(tokens, model_type)
        aisuite_enabled = self.aisuite_config.get("enable_aisuite", False)
        start_time = datetime.now()
        for model_name in model_list:
            primary_model = model_name.split("_")[0]
            stream = False
            # 千问模型开启联网内容插件需要设置extra_body={"enable_search": True}
            extra_body = {
                "enable_search": True, # 启用互联网搜索
                "search_options": {
                    "enable_source": True, # 返回结果中展示搜索到的信息
                    "forced_search": False, # 不强制开启搜索
                    "search_strategy": "standard", # 在请求时搜索5条互联网信息
                }
            }
            # 重尝试3次
            for retry_num in range(3):
                try:
                    # 豆包模型开启联网内容插件需要调用智能体
                    if aisuite_enabled:
                        response = await AIService.aisuite(
                            "empty", prompt=prompt,
                            stream=stream,
                            model_name=model_name,
                            extra_body=extra_body,
                            environment=config["Environment"]["env"],
                        )
                    else:
                        response = await getattr(AIService, primary_model)(
                            model_name, prompt, stream=stream, extra_body=extra_body, custom_resp="chatCompletion"
                        )
                    result, prompt_tokens, completion_tokens = await self.from_model_get_result(model_name, response, data, step_num)
                    # 输出格式问题，针对当前模型重试
                    if result == False:
                        logger.error(f"{self.log_prefix}retry_num: {retry_num + 1}, model {model_name} failed")
                        continue
                    # 记录成功日志
                    AnalysisManager.save_correction_log(data, prompt, result, model_name, starttime=start_time, endtime=datetime.now(), step_num=step_num,
                                                        prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
                    return result
                except Exception as error:
                    logger.error(f"{self.log_prefix}model {model_name} failed: {str(error)}", exc_info=True)
                    # 非格式问题，切换模型
                    break

        return False
    
    async def get_answer_point_format_result(self, model_cls, data, tokens):
        format_prompt = await get_answer_point_format_prompt(data)
        if format_prompt is None:
            raise Exception("获取答案要点生成格式处理prompt失败")
        format_result = await model_cls.round_parse(data, tokens, format_prompt, step_num=22)
        if not format_result:
            logger.error(f"{self.log_prefix}获取答案要点生成格式处理结果失败")
            raise Exception("获取答案要点生成格式处理结果失败")
        return format_result
    
    async def get_answer_point_result(self, model_cls, data, tokens):
        answer_point_prompt = await get_answer_point_prompt(data)
        if answer_point_prompt is None:
            raise Exception("获取答案要点生成prompt失败")
        answer_point_result = await model_cls.round_parse(data, tokens, answer_point_prompt, step_num=21, model_type="answer_point")
        if not answer_point_result:
            logger.error(f"{self.log_prefix}获取答案要点生成结果失败")
            raise Exception("要点生成失败")
        return answer_point_result
    
    async def get_answer_point_correction_result(self, model_cls, data, result, tokens):
        correction_prompt = await get_answer_point_correction_prompt(data, result)
        if correction_prompt is None:
            raise Exception("获取答案要点二次校正prompt失败")
        correction_result = await model_cls.round_parse(data, tokens, correction_prompt, step_num=23, model_type="answer_point")
        if not correction_result:
            logger.error(f"{self.log_prefix}获取答案要点二次校正结果失败")
            raise Exception("要点分数与总分不符")
        return correction_result