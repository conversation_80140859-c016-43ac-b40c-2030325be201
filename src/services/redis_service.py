import time
import socket
import asyncio
import platform
from utils.logger import logger
import redis.asyncio as aioredis
from config.settings import config
from typing import Optional, Dict, List
from redis.exceptions import NoScriptError
from redis.asyncio.connection import ConnectionPool


class RedisConnectionManager:
    """Redis连接管理器 - 具备自动重连和健康检查功能"""
    
    def __init__(self, 
                 host: str = "localhost", 
                 port: int = 6379, 
                 db: int = 0,
                 password: str = "",
                 max_connections: int = 20,
                 retry_on_timeout: bool = True,
                 health_check_interval: int = 30,
                 max_reconnect_attempts: int = 5,
                 reconnect_delay: float = 1.0):
        
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.max_connections = max_connections
        self.retry_on_timeout = retry_on_timeout
        self.health_check_interval = health_check_interval
        self.max_reconnect_attempts = max_reconnect_attempts
        self.reconnect_delay = reconnect_delay
        
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[aioredis.Redis] = None
        self._is_healthy = False
        self._last_health_check = 0
        self._reconnect_attempts = 0
        self._health_check_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def _create_connection_pool(self) -> ConnectionPool:
        """创建连接池"""
        try:
            options = {
                "socket_connect_timeout": 10,
                "socket_timeout": 15,
                "socket_keepalive": True,
            }

            # 仅在 Linux 上设置 socket_keepalive_options 参数
            if platform.system() == "Linux":
                options["socket_keepalive_options"] = {
                    socket.TCP_KEEPIDLE: 60,    # 60秒空闲后开始探测（1分钟）
                    socket.TCP_KEEPINTVL: 30,   # 每30秒探测一次
                    socket.TCP_KEEPCNT: 3       # 3次失败后断开
                }
            pool = ConnectionPool(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                max_connections=self.max_connections,
                retry_on_timeout=self.retry_on_timeout,
                decode_responses=True,
                **options
            )
            
            # 测试连接池
            test_client = aioredis.Redis(connection_pool=pool)
            try:
                await test_client.ping()
                logger.info(f"✅ Redis连接池创建成功 - {self.host}:{self.port}/{self.db}")
            finally:
                await test_client.close()  # 确保测试客户端被正确关闭
            
            return pool
            
        except Exception as e:
            logger.error(f"❌ 创建Redis连接池失败: {e}")
            raise
    
    async def initialize(self):
        """初始化连接管理器"""
        async with self._lock:
            if self._pool is None:
                self._pool = await self._create_connection_pool()
                self._client = aioredis.Redis(connection_pool=self._pool)
                self._is_healthy = True
                self._reconnect_attempts = 0
                
                # 启动健康检查任务
                if self._health_check_task is None:
                    self._health_check_task = asyncio.create_task(self._health_check_loop())
                
                logger.info("🔄 Redis连接管理器初始化完成")
    
    async def get_client(self) -> aioredis.Redis:
        """获取Redis客户端 - 具备自动重连功能"""
        if not self._is_healthy or self._client is None:
            await self._ensure_connection()
        
        return self._client
    
    async def _ensure_connection(self):
        """确保连接可用 - 自动重连逻辑"""
        async with self._lock:
            if self._is_healthy and self._client is not None:
                return
                
            try:
                logger.info("🔄 尝试重新建立Redis连接...")
                
                # 清理旧连接
                await self._cleanup_connections()
                
                # 创建新连接
                self._pool = await self._create_connection_pool()
                self._client = aioredis.Redis(connection_pool=self._pool)
                
                # 测试连接
                await self._client.ping()
                
                self._is_healthy = True
                self._reconnect_attempts = 0
                logger.info("✅ Redis连接重建成功")
                
            except Exception as e:
                self._reconnect_attempts += 1
                self._is_healthy = False
                
                if self._reconnect_attempts >= self.max_reconnect_attempts:
                    logger.error(f"❌ Redis重连失败，已达最大尝试次数 {self.max_reconnect_attempts}: {e}")
                    raise
                else:
                    logger.warning(f"⚠️ Redis重连失败 (尝试 {self._reconnect_attempts}/{self.max_reconnect_attempts}): {e}")
                    await asyncio.sleep(self.reconnect_delay * self._reconnect_attempts)
                    raise
    
    async def _health_check_loop(self):
        """定期健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            if self._client:
                await self._client.ping()
                self._is_healthy = True
                self._last_health_check = time.time()
                logger.debug("✅ Redis健康检查通过")
            else:
                self._is_healthy = False
                
        except Exception as e:
            logger.warning(f"⚠️ Redis健康检查失败: {e}")
            self._is_healthy = False
    
    async def _cleanup_connections(self):
        """清理连接资源"""
        try:
            if self._client:
                await self._client.close()
                self._client = None
                
            if self._pool:
                await self._pool.disconnect()
                self._pool = None
                
        except Exception as e:
            logger.error(f"清理连接资源失败: {e}")
    
    async def close(self):
        """关闭连接管理器"""
        async with self._lock:
            # 停止健康检查任务
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
                self._health_check_task = None
            
            # 清理连接
            await self._cleanup_connections()
            self._is_healthy = False
            
            logger.info("🔚 Redis连接管理器已关闭")
    
    async def execute_with_retry(self, operation, *args, max_retries: int = 3, **kwargs):
        """执行Redis操作并自动重试"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                client = await self.get_client()
                return await operation(client, *args, **kwargs)
                
            except (aioredis.ConnectionError, aioredis.TimeoutError, OSError) as e:
                last_exception = e
                logger.warning(f"⚠️ Redis操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                # 标记连接为不健康，触发重连
                self._is_healthy = False
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5 * (2 ** attempt))  # 指数退避
                    continue
                else:
                    break
                    
            except Exception as e:
                # 非连接相关的错误直接抛出
                logger.error(f"❌ Redis操作异常: {e}")
                raise
        
        raise last_exception


class RedisDeduplicationService:
    """消息去重服务 - 基于Redis的跨进程幂等性保证"""
    
    _connection_manager: Optional[RedisConnectionManager] = None
    _key_prefix = "kafka_processed_async:"
    _ttl_seconds = None  # 初始化时从配置中读取
    _lock = asyncio.Lock()

    @classmethod
    async def initialize(cls):
        """初始化去重服务"""
        if cls._connection_manager is not None:
            return
        
        async with cls._lock:
            if cls._connection_manager is None:
                redis_config = config["REDIS"]
                # 从配置中读取TTL参数，默认为1天
                ttl_days = int(redis_config.get("deduplication_ttl_days", 1))
                cls._ttl_seconds = int(ttl_days * 24 * 3600)
                
                cls._connection_manager = RedisConnectionManager(
                    host=redis_config.get("host", "localhost"),
                    port=int(redis_config.get("port", 6379)),
                    db=int(redis_config.get("db", 0)),
                    password=redis_config.get("password", ""),
                    max_connections=int(redis_config.get("max_connections", 20))
                )
                await cls._connection_manager.initialize()
                logger.info(f"🔄 消息去重服务初始化完成，TTL: {ttl_days}天 ({cls._ttl_seconds}秒)")
    
    @classmethod
    def _generate_message_key(cls, group_id: str, msg) -> str:
        """生成消息的Redis key"""
        return f"{cls._key_prefix}{group_id}:{msg.topic}:{msg.partition}:{msg.offset}"
    
    @classmethod
    async def is_message_processed(cls, group_id: str, msg) -> bool:
        """检查消息是否已经处理过"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        async def _check_operation(client: aioredis.Redis, group_id: str, msg) -> bool:
            key = cls._generate_message_key(group_id, msg)
            result = await client.exists(key)
            return bool(result)
        
        try:
            return await cls._connection_manager.execute_with_retry(_check_operation, group_id, msg)
        except Exception as e:
            logger.error(f"❌ 检查消息处理状态失败: {e}")
            return False
    
    @classmethod
    async def mark_message_processed(cls, group_id: str, msg):
        """标记消息已处理"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        async def _mark_operation(client: aioredis.Redis, group_id: str, msg):
            key = cls._generate_message_key(group_id, msg)
            await client.setex(key, cls._ttl_seconds, "1")
        
        try:
            await cls._connection_manager.execute_with_retry(_mark_operation, group_id, msg)
        except Exception as e:
            logger.error(f"❌ 标记消息处理状态失败: {e}")
    
    @classmethod
    async def batch_check_messages(cls, group_id: str, messages: List) -> Dict[str, bool]:
        """批量检查消息处理状态 - 性能优化"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        if not messages:
            return {}
        
        async def _batch_check_operation(client: aioredis.Redis, group_id: str, messages: List) -> Dict[str, bool]:
            # 预先构建所有key
            keys = []
            message_id_map = {}
            
            for msg in messages:
                key = cls._generate_message_key(group_id, msg)
                message_id = f"{group_id}:{msg.topic}:{msg.partition}:{msg.offset}"
                keys.append(key)
                message_id_map[key] = message_id
            
            # 批量检查存在性
            if keys:
                pipe = client.pipeline()
                for key in keys:
                    pipe.exists(key)
                results = await pipe.execute()
                
                # 构造结果字典
                status_dict = {}
                for i, key in enumerate(keys):
                    message_id = message_id_map[key]
                    status_dict[message_id] = bool(results[i])
                
                return status_dict
            
            return {}
        
        try:
            return await cls._connection_manager.execute_with_retry(_batch_check_operation, group_id, messages)
        except Exception as e:
            logger.error(f"❌ 批量检查消息状态失败: {e}")
            return {}
    
    @classmethod
    async def batch_mark_messages_processed(cls, group_id: str, messages: List):
        """批量标记消息已处理"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        if not messages:
            return
        
        async def _batch_mark_operation(client: aioredis.Redis, group_id: str, messages):
            pipe = client.pipeline()
            
            for msg in messages:
                key = cls._generate_message_key(group_id, msg)
                pipe.setex(key, cls._ttl_seconds, "1")
            
            await pipe.execute()
        
        try:
            await cls._connection_manager.execute_with_retry(_batch_mark_operation, group_id, messages)
        except Exception as e:
            logger.error(f"❌ 批量标记消息状态失败: {e}")
    
    @classmethod
    async def cleanup(cls):
        """清理资源"""
        if cls._connection_manager:
            await cls._connection_manager.close()
            cls._connection_manager = None
            logger.info("🔚 消息去重服务已清理")


class RedisOffsetManager:
    """Redis Offset管理器 - 用于备份和恢复Kafka offset"""
    
    _connection_manager: Optional[RedisConnectionManager] = None
    _key_prefix = "kafka_offset_backup_async:"
    _ttl_seconds = None  # 初始化时从配置中读取
    _lock = asyncio.Lock()
    _lua_lock = asyncio.Lock()
    
    _lua_script = """
        local key = KEYS[1]
        local new_offset = tonumber(ARGV[1])
        local ttl = tonumber(ARGV[2])
        local current_offset = redis.call('GET', key)
        if not current_offset or new_offset > tonumber(current_offset) then
            redis.call('SET', key, new_offset)
            if ttl > 0 then
                redis.call('EXPIRE', key, ttl)
            end
            return 1 -- 返回1表示成功更新
        end
        return 0 -- 返回0表示未更新
    """

    _lua_batch_script = """
        local ttl = tonumber(ARGV[#ARGV])
        for i = 1, #KEYS do
            local key = KEYS[i]
            local new_offset = tonumber(ARGV[i])
            local current_offset = redis.call('GET', key)
            
            if (not current_offset) or (new_offset > tonumber(current_offset)) then
                redis.call('SET', key, new_offset)
                if ttl > 0 then
                    redis.call('EXPIRE', key, ttl)
                end
            end
        end
        return 1
    """
    _lua_script_sha = None
    _lua_batch_script_sha = None

    @classmethod
    async def initialize(cls):
        """初始化offset管理器"""
        if cls._connection_manager is not None:
            return
            
        async with cls._lock:
            if cls._connection_manager is None:
                redis_config = config["REDIS"]
                # 从配置中读取TTL参数，默认为14天
                ttl_days = int(redis_config.get("offset_ttl_days", 14))
                cls._ttl_seconds = int(ttl_days * 24 * 3600)
                
                cls._connection_manager = RedisConnectionManager(
                    host=redis_config.get("host", "localhost"),
                    port=int(redis_config.get("port", 6379)),
                    db=int(redis_config.get("db", 0)),
                    password=redis_config.get("password", ""),
                    max_connections=int(redis_config.get("max_connections", 20))
                )
                await cls._connection_manager.initialize()
                logger.info(f"🔄 Offset管理器初始化完成，TTL: {ttl_days}天 ({cls._ttl_seconds}秒)")

    @classmethod
    async def _load_lua_script(cls, client):
        """加载Lua脚本"""
        async with cls._lua_lock:
            if cls._lua_batch_script is not None and cls._lua_batch_script_sha is None:
                cls._lua_batch_script_sha = await client.script_load(cls._lua_batch_script)
                logger.info("✅ 批量 Redis Lua 脚本注册成功")
            if cls._lua_script is not None and cls._lua_script_sha is None:
                cls._lua_script_sha = await client.script_load(cls._lua_script)
                logger.info("✅ 单条 Redis Lua 脚本注册成功")

    @classmethod
    def _generate_offset_key(cls, group_id: str, topic: str, partition: int) -> str:
        """生成offset的Redis key"""
        return f"{cls._key_prefix}{group_id}:{topic}:{partition}"
    
    @classmethod
    async def save_offset(cls, group_id: str, topic: str, partition: int, offset: int):
        """保存offset到Redis"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        async def _save_operation(client: aioredis.Redis, group_id, topic, partition, offset):
            key = cls._generate_offset_key(group_id, topic, partition)
            await client.setex(key, cls._ttl_seconds, str(offset))
        
        try:
            await cls._connection_manager.execute_with_retry(
                _save_operation, group_id, topic, partition, offset
            )
            logger.debug(f"💾 保存offset: {group_id}:{topic}:{partition}:{offset}")
        except Exception as e:
            logger.error(f"❌ 保存offset失败: {e}")
            
    @classmethod
    async def save_offset_lua(cls, group_id: str, topic: str, partition: int, offset: int):
        """使用Lua脚本保存offset（仅在新值更大时更新）"""
        if cls._connection_manager is None:
            await cls.initialize()

        key = cls._generate_offset_key(group_id, topic, partition)
        args = [str(offset), str(cls._ttl_seconds)]

        async def _lua_save_operation(client):
            try:
                if cls._lua_script_sha is None:
                    await cls._load_lua_script(client)
                return await client.evalsha(cls._lua_script_sha, 1, key, *args)
            except NoScriptError as e:
                logger.warning("⚠️ Lua单条保存offset脚本失效，重新注册")
                cls._lua_script_sha = await client.script_load(cls._lua_script)
                return await client.evalsha(cls._lua_script_sha, 1, key, *args)

        try:
            await cls._connection_manager.execute_with_retry(_lua_save_operation)
            logger.debug(f"💾 (Lua) 保存offset: {group_id}:{topic}:{partition}:{offset}")
        except Exception as e:
            logger.error(f"❌ (Lua) 保存offset失败: {e}")

    @classmethod
    async def get_offset(cls, group_id: str, topic: str, partition: int) -> Optional[int]:
        """从Redis获取offset"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        async def _get_operation(client: aioredis.Redis, group_id, topic, partition) -> Optional[int]:
            key = cls._generate_offset_key(group_id, topic, partition)
            offset_str = await client.get(key)
            if offset_str:
                return int(offset_str)
            return None
        
        try:
            result = await cls._connection_manager.execute_with_retry(
                _get_operation, group_id, topic, partition
            )
            if result is not None:
                logger.debug(f"📖 获取offset: {group_id}:{topic}:{partition} = {result}")
            return result
        except Exception as e:
            logger.error(f"❌ 获取offset失败: {e}")
            return None
            
    @classmethod
    async def batch_save_offsets_lua(cls, group_id: str, offsets: Dict[tuple, int]):
        """使用Lua脚本批量保存offset（仅新值更大时更新）"""
        if cls._connection_manager is None:
            await cls.initialize()

        if not offsets:
            return

        keys = []
        args = []

        for (topic, partition), offset in offsets.items():
            key = cls._generate_offset_key(group_id, topic, partition)
            keys.append(key)
            args.append(str(offset))

        args.append(str(cls._ttl_seconds))  # TTL 作为最后参数

        async def _lua_batch_save_operation(client):
            try:
                if cls._lua_batch_script_sha is None:
                    await cls._load_lua_script(client)
                return await client.evalsha(cls._lua_batch_script_sha, len(keys), *(keys + args))
            except NoScriptError as e:
                logger.warning("⚠️ Lua批量保存offset脚本失效，重新注册")
                cls._lua_batch_script_sha = await client.script_load(cls._lua_batch_script)
                return await client.evalsha(cls._lua_batch_script_sha, len(keys), *(keys + args))

        try:
            await cls._connection_manager.execute_with_retry(_lua_batch_save_operation)
            logger.debug(f"💾 (Lua) 批量保存offset: {len(offsets)} 个记录")
        except Exception as e:
            logger.error(f"❌ (Lua) 批量保存offset失败: {e}")

    @classmethod
    async def batch_save_offsets(cls, group_id: str, offsets: Dict[tuple, int]):
        """批量保存offset - 性能优化"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        if not offsets:
            return
        
        async def _batch_save_operation(client: aioredis.Redis, group_id, offsets):
            pipe = client.pipeline()
            
            for (topic, partition), offset in offsets.items():
                key = cls._generate_offset_key(group_id, topic, partition)
                pipe.setex(key, cls._ttl_seconds, str(offset))
            
            await pipe.execute()
        
        try:
            await cls._connection_manager.execute_with_retry(_batch_save_operation, group_id, offsets)
            logger.debug(f"💾 批量保存offset: {len(offsets)} 个记录")
        except Exception as e:
            logger.error(f"❌ 批量保存offset失败: {e}")
    
    @classmethod
    async def batch_get_offsets(cls, group_id: str, topic_partitions: List[tuple]) -> Dict[tuple, Optional[int]]:
        """批量获取offset - 性能优化"""
        if cls._connection_manager is None:
            await cls.initialize()
        
        if not topic_partitions:
            return {}
        
        async def _batch_get_operation(client: aioredis.Redis, group_id, topic_partitions) -> Dict[tuple, Optional[int]]:
            keys_and_tps = []  # 预处理key和topic_partition的映射
            
            for topic, partition in topic_partitions:
                key = cls._generate_offset_key(group_id, topic, partition)
                keys_and_tps.append((key, (topic, partition)))
            
            # 批量获取 - 性能优化
            if keys_and_tps:
                pipe = client.pipeline()
                for key, _ in keys_and_tps:
                    pipe.get(key)
                results = await pipe.execute()
                
                # 构造结果
                offset_dict = {}
                for i, (key, tp) in enumerate(keys_and_tps):
                    offset_str = results[i]
                    offset_dict[tp] = int(offset_str) if offset_str else None
                
                return offset_dict
            
            return {}
        
        try:
            result = await cls._connection_manager.execute_with_retry(
                _batch_get_operation, group_id, topic_partitions
            )
            logger.debug(f"📖 批量获取offset: {len(topic_partitions)} 个记录")
            return result
        except Exception as e:
            logger.error(f"❌ 批量获取offset失败: {e}")
            return {}
    
    @classmethod
    async def cleanup(cls):
        """清理资源"""
        if cls._connection_manager:
            await cls._connection_manager.close()
            cls._connection_manager = None
            logger.info("🔚 Offset管理器已清理")


# 全局清理函数
async def cleanup_all_async_redis_services():
    """清理所有Redis服务"""
    await RedisDeduplicationService.cleanup()
    await RedisOffsetManager.cleanup()
    logger.info("🧹 所有Redis服务已清理")