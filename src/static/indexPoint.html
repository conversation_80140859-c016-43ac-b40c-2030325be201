<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI申论批改-要点生成</title>
    <!-- 引入 Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 2rem;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>AI申论批改-要点生成</h1>
    <form id="myForm">
        <div class="mb-3">
            <label for="inputText" class="form-label">输入JSON格式的题目文本信息</label>
            <textarea class="form-control" id="inputText" rows="5" placeholder="请输入文本"></textarea>
        </div>
        <button type="submit" class="btn btn-primary">提交</button>
    </form>
    <div id="result" class="mt-3"></div>
</div>

<!-- 引入 Bootstrap JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 监听表单提交事件
    document.getElementById('myForm').addEventListener('submit', async function (event) {
        event.preventDefault(); // 阻止表单默认提交行为

        // 获取用户输入的文本
        const inputText = document.getElementById('inputText').value;

        // 显示提示信息，告知用户请求已提交
        document.getElementById('result').innerHTML = '<p>请求已经提交，请勿重复点击。</p>';

        try {
            // 解析用户输入的JSON格式文本
            const parsedJson = JSON.parse(inputText);

            // 发送POST请求到后端
            const response = await fetch('/point/analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(parsedJson)
            });

            // 如果响应状态码不是200-299，抛出错误
            if (!response.ok) {
                const errorText = await response.text(); // 获取错误信息
                throw new Error(`HTTP Error ${response.status}: ${errorText}`);
            }

            // 解析响应结果（后端返回的是一个元组：[round_result, elapsed_time]）
            const result = await response.json();

            // 提取消耗时间和批改结果
            const elapsedTime = result[1]; // 消耗时间
            const roundResult = result[0]; // 批改结果

            // 显示消耗时间和结果
            document.getElementById('result').innerHTML = `
                <p>消耗时间：${elapsedTime}</p>
                <pre>${JSON.stringify(roundResult, null, 2)}</pre>
            `;
        } catch (error) {
            // 捕获并处理错误
            console.error('Error processing request:', error);
            document.getElementById('result').innerHTML = `<p>发生错误：${error.message}</p>`;
        }
    });
</script>
</body>
</html>