
from utils.logger import logger


class ErrorCode:
    """错误码枚举"""
    # 主观题批改错误码
    CORRECTION_DATA_VALIDATION_ERROR = "C001"  # 数据校验错误
    CORRECTION_MODEL_ERROR = "C002"  # 模型错误
    CORRECTION_OTHER_ERROR = "C999"  # 其他错误
    
    # 答案要点生成错误码
    ANSWER_POINT_DATA_VALIDATION_ERROR = "A001"  # 数据校验错误
    ANSWER_POINT_MODEL_ERROR = "A002"  # 模型错误
    ANSWER_POINT_SCORE_MISMATCH_ERROR = "A003"  # 要点分数与总分不符
    ANSWER_POINT_OTHER_ERROR = "A999"  # 其他错误
    
    # 要点定位错误码
    LOCATION_DATA_VALIDATION_ERROR = "L001"  # 数据校验错误
    LOCATION_MODEL_ERROR = "L002"  # 模型错误
    LOCATION_OTHER_ERROR = "L999"  # 其他错误

    # 通用未知错误码
    UNKNOWN_ERROR_TYPE = "U999"  # 未知错误类型


class ErrorClassifier:
    """错误分类器"""
    
    # C端批改错误分类映射
    CORRECTION_ERROR_MAPPING = {
        # 数据校验错误
        "数据结构有误": ErrorCode.CORRECTION_DATA_VALIDATION_ERROR,
        
        # 模型错误 - 所有涉及prompt失败和模型返回失败的错误
        "要点划线批改模块获取prompt模板失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "逻辑条理扣分模块获取prompt模板失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取token数量失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取文章标题提取prompt失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取文章标题失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取逐句点评prompt失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取要点划线批改结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取逻辑点评结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "最终点评模块获取prompt模板失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取最终点评结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取文章分级、标题扣分、文采加分prompt失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取文章分级、标题扣分、文采加分结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取逐句点评结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取语言表达点评、审题立意点评、论据点评、逻辑条理点评、写作建议prompt失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "获取语言表达点评结果失败": ErrorCode.CORRECTION_MODEL_ERROR,
        "触发敏感词": ErrorCode.CORRECTION_MODEL_ERROR,
        
        # 其他错误
        "分数计算失败": ErrorCode.CORRECTION_OTHER_ERROR,
    }
    
    # 答案要点生成错误分类映射
    ANSWER_POINT_ERROR_MAPPING = {
        # 数据校验错误
        "数据结构有误": ErrorCode.ANSWER_POINT_DATA_VALIDATION_ERROR,
        
        # 模型错误
        "获取答案要点生成格式处理prompt失败": ErrorCode.ANSWER_POINT_MODEL_ERROR,
        "获取答案要点生成格式处理结果失败": ErrorCode.ANSWER_POINT_MODEL_ERROR,
        "获取答案要点生成prompt失败": ErrorCode.ANSWER_POINT_MODEL_ERROR,
        "要点生成失败": ErrorCode.ANSWER_POINT_MODEL_ERROR,
        "获取答案要点二次校正prompt失败": ErrorCode.ANSWER_POINT_MODEL_ERROR,
        
        # 要点分数与总分不符
        "要点分数与总分不符": ErrorCode.ANSWER_POINT_SCORE_MISMATCH_ERROR,
    }
    
    # 要点定位错误分类映射
    LOCATION_ERROR_MAPPING = {
        # 数据校验错误
        "数据结构有误": ErrorCode.LOCATION_DATA_VALIDATION_ERROR,
        
        # 模型错误
        "获取要点定位prompt模板失败": ErrorCode.LOCATION_MODEL_ERROR,
        "获取token数量失败": ErrorCode.LOCATION_MODEL_ERROR,
        "获取要点定位结果失败": ErrorCode.LOCATION_MODEL_ERROR,
    }
    

    DESCRIPTIONS = {
        # 主观题批改错误
        ErrorCode.CORRECTION_DATA_VALIDATION_ERROR: "数据校验错误",
        ErrorCode.CORRECTION_MODEL_ERROR: "模型错误",
        ErrorCode.CORRECTION_OTHER_ERROR: "其他错误",

        # 答案要点生成错误
        ErrorCode.ANSWER_POINT_DATA_VALIDATION_ERROR: "数据校验错误",
        ErrorCode.ANSWER_POINT_MODEL_ERROR: "模型错误",
        ErrorCode.ANSWER_POINT_SCORE_MISMATCH_ERROR: "要点分数与总分不符",
        ErrorCode.ANSWER_POINT_OTHER_ERROR: "其他错误",
        
        # 要点定位错误
        ErrorCode.LOCATION_DATA_VALIDATION_ERROR: "数据校验错误",
        ErrorCode.LOCATION_MODEL_ERROR: "模型错误",
        ErrorCode.LOCATION_OTHER_ERROR: "其他错误",

        # 通用未知错误
        ErrorCode.UNKNOWN_ERROR_TYPE: "未知错误类型",
    }

    @classmethod
    def get_error_code(cls, error_message, error_type="correction"):
        """
        根据错误信息获取错误码
        
        Args:
            error_message: 错误信息
            error_type: 错误类型，"correction"、"answer_point" 或 "location"
            
        Returns:
            错误码
        """
        if error_type == "correction":
            mapping = cls.CORRECTION_ERROR_MAPPING
            default_error_code = ErrorCode.CORRECTION_OTHER_ERROR
        elif error_type == "answer_point":
            mapping = cls.ANSWER_POINT_ERROR_MAPPING
            default_error_code = ErrorCode.ANSWER_POINT_OTHER_ERROR
        elif error_type == "location":
            mapping = cls.LOCATION_ERROR_MAPPING
            default_error_code = ErrorCode.LOCATION_OTHER_ERROR
        else:
            logger.error(f"unknown error_type={error_type}")
            return ErrorCode.UNKNOWN_ERROR_TYPE
        
        # 遍历映射，查找匹配的错误信息
        for key_phrase, error_code in mapping.items():
            if key_phrase in error_message:
                return error_code
        
        # 如果没有匹配到，返回默认的其他错误码
        return default_error_code
    
    @classmethod
    def get_error_description(cls, error_code):
        """
        根据错误码获取错误描述
        
        Args:
            error_code: 错误码
            
        Returns:
            错误描述
        """
        return cls.DESCRIPTIONS.get(error_code, "未知错误类型")