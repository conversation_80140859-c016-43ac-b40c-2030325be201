import os
import logging
from config.settings import config
from concurrent_log_handler import ConcurrentTimedRotatingFileHandler


class LogManager:
    """
    A class for managing logs with customizable levels and handlers.
    """

    def __init__(self, name=__name__, log_level=logging.INFO, log_dir="logs"):
        self.logger = logging.getLogger(name)
        # 安全获取 config 中的日志等级配置
        config_log_level_str = config.get("Log", {}).get("level", "").upper()
        # 尝试解析日志等级
        config_log_level = getattr(logging, config_log_level_str, None)
        # 如果 config 无效，则使用默认传入的 log_level
        log_level = config_log_level if config_log_level is not None else log_level
        self.logger.setLevel(log_level)

        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, "app.log")
        handler = ConcurrentTimedRotatingFileHandler(log_file, when="midnight", interval=1, backupCount=7)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def debug(self, message):
        self.logger.debug(message)

    def info(self, message):
        self.logger.info(message)

    def warning(self, message):
        self.logger.warning(message)

    def error(self, message, exc_info=False):
        if exc_info:
            self.logger.error(message, exc_info=exc_info)
        else:
            self.logger.error(message)

    def critical(self, message, exc_info=False):
        if exc_info:
            self.logger.critical(message, exc_info=exc_info)
        else:
            self.logger.critical(message)


logger = LogManager()