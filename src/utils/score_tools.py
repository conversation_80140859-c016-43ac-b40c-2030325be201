import re
from utils.logger import logger
from utils.general_tools import convert_chinese_punctuation_to_english
from decimal import Decimal, ROUND_HALF_UP, ROUND_UP, ROUND_DOWN, ROUND_HALF_EVEN


class score:

    @classmethod
    def create_answer_score_map(cls, question_key_points):
        """
        创建一个答案要点到其分数的映射
        :param question_key_points: 问题的要点信息
        :return: 答案要点到其分数的映射
        """
        return {item['keyPointId']: item["score"] for index, item in enumerate(question_key_points)}

    @classmethod
    def create_convert_score_map(cls, question_key_points):
        """
        创建一个答案要点到其折算分数的映射
        :param question_key_points: 问题的要点信息
        :return: 答案要点到其折算分数的映射
        """
        convert_score_map = {}
        for item in question_key_points:
            if 'convertScore' in item:
                convert_score_map[item['keyPointId']] = item['convertScore']
            else:
                convert_score_map[item['keyPointId']] = item['score']
        return convert_score_map

    @classmethod
    def process_point_score(cls, item, answer_score):
        """
        处理回答要点的分数，包括类型转换、负数处理、分数上限调整和倍数调整
        :param item: 回答要点的信息
        :param answer_score: 答案要点的分数
        :return: 处理后的回答要点分数
        """
        answer_classification = item["回答要点分类"]
        # 如果回答要点分类是准确，则直接返回答案要点的分数, 如果回答要点分类是遗漏，则直接返回0
        if answer_classification == "准确":
            return answer_score
        elif answer_classification == "遗漏":
            return 0
        
        point_score = get_clear_score(item["要点得分"])
        # 如果分数小于等于0，返回0分
        if point_score <= 0:
            return 0
        
        # 答案要点分数小于0.5，应用要点分过低适配规则
        if answer_score < 0.5:
            point_score = cls.apply_low_score_adaptation(point_score, answer_score)
        else:
            # 如果回答要点分数大于答案要点分数，修改成答案要点的满分
            point_score = min(point_score, answer_score)
            # 如果分数不是0.5的倍数，调整成小于答案要点的分数且是0.5的倍数
            point_score = cls.adjust_to_half_multiple(point_score, answer_score)
        
        return point_score

    @classmethod
    def adjust_to_half_multiple(cls, point_score, answer_score):
        """
        调整分数为0.5的倍数且不超过答案要点的分数
        :param point_score: 回答要点的分数
        :param answer_score: 答案要点的分数
        :return: 调整后的分数
        """
        if point_score % 0.5 != 0:
            point_score = (int(point_score * 2) + 1) / 2
            point_score = min(point_score, answer_score)
        return point_score
    
    @classmethod
    def apply_low_score_adaptation(cls, point_score, answer_score):
        """
        应用要点分过低适配规则
        """
        # 如果回答要点的分数大于等于答案要点的分数，则将回答要点的分数除以2
        if point_score >= answer_score:
            point_score = answer_score / 2

        # 保留小数点后一位
        point_score = custom_round(point_score, 1)
        # 得分最小为0.1
        point_score = max(point_score, 0.1)
        # 得分最大为答案要点的分数
        point_score = min(point_score, answer_score)
        return point_score

    @classmethod
    def convert_score(cls, original_score, conversion_ratio, convert_score_upper, convert_score_lower=0.1, digits=1, method='down'):
        """
        根据折算比例计算折算后的分数
        
        Args:
            original_score: 原始分数
            conversion_ratio: 折算比例
            convert_score_upper: 折算后的上限分数
            convert_score_lower: 折算后的下限分数 默认0.1
            digits: 保留的小数位数 默认1
            method: 舍入方式 默认'down' 始终向下取整

        Returns:
            折算后的分数
        """
        if original_score <= 0:
            return 0
        
        if conversion_ratio == 1:
            return original_score
            
        # 根据折算比例计算折算后的分数
        converted_score = original_score * conversion_ratio
        
        # 根据舍入方式进行舍入
        converted_score = custom_round(converted_score, digits, method)

        # 如果折算后的分数小于下限分数，则改成下限分数
        converted_score = max(converted_score, convert_score_lower)
        
        # 如果折算后的分数大于上限分数，则改成上限分数
        converted_score = min(converted_score, convert_score_upper)
        
        return converted_score

    @classmethod
    async def correct_score(cls, data, line_result):
        # 获取问题的要点信息
        question_key_points = data['keyPoint']['questionKeyPoints']
        # 创建一个答案要点到其分数的映射
        answer_score_map = cls.create_answer_score_map(question_key_points)
        # 获取折算比例
        conversion_ratio = data['keyPoint']['conversionRatio']
        has_conversion = data['keyPoint']['hasConversion']
        
        # 处理每个回答要点的分数
        for index, item in enumerate(line_result):
            # 去除回答要点分类的空格
            item['回答要点分类'] = item['回答要点分类'].strip()
            # 获取答案要点的分数
            answer_score = answer_score_map[item['keyPointId']]
            # 处理并获取回答要点的分数
            point_score = cls.process_point_score(item, answer_score)
            
            # 如果折算比例不为1，进行折算
            if has_conversion:
                # 创建一个答案要点到其折算分数的映射
                convert_score_map = cls.create_convert_score_map(question_key_points)
                # 获取折算后的上限分数
                convert_answer_score = convert_score_map[item['keyPointId']]
                if point_score == answer_score:
                    converted_score = convert_answer_score
                else:
                    # 折算分数
                    converted_score = cls.convert_score(point_score, conversion_ratio, convert_answer_score)
                item["要点得分"] = converted_score
            else:
                item["要点得分"] = point_score
                
        return line_result

    @classmethod
    def process_format_score(cls, format_score, answer_score):
        """
        处理格式得分
        """
        if not isinstance(format_score, (str, int, float)):
            return 0
        
        format_score = get_clear_score(format_score)
        # 如果分数不是0.5的倍数，调整成小于答案要点的分数且是0.5的倍数
        format_score = cls.adjust_to_half_multiple(format_score, answer_score)
        return format_score
    
    @classmethod
    def process_format_field(cls, content, score_text, max_score):
        """
        处理字段内容和分数
        
        Args:
            content: 字段内容
            score_text: 字段分数文本
            max_score: 最大分数
            
        Returns:
            tuple: (处理后的内容, 处理后的分数)
        """
        # 验证内容
        if not isinstance(content, str) or content.strip() in ["无", ""]:
            return "无", 0
        
        # 处理分数
        processed_score = cls.process_format_score(score_text, max_score)
        if processed_score <= 0:
            return "无", 0
            
        return content.strip(), processed_score
    
    @classmethod
    async def correct_format_score(cls, data, format_result):
        """
        处理格式得分，确保每个格式得分小于等于格式总分且为0.5的倍数
        """
        result = {}
        has_conversion = data['keyPoint']['hasConversion']
        format_points = data["keyPoint"]["formatPoints"]
        # 获取折算比例
        conversion_ratio = data['keyPoint']['conversionRatio']
        # 获取折算后的格式分数上限
        convert_format_points = data["keyPoint"]["convertFormatPoints"]
        
        # 提取落款信息
        signature_dict = format_result.get('回答落款', {}) if isinstance(format_result.get('回答落款'), dict) else {}
        signature_score_dict = format_result.get('回答落款得分', {}) if isinstance(format_result.get('回答落款得分'), dict) else {}
        
        # 处理各字段
        fields = [
            {'key': '回答标题', 'content': format_result.get('回答标题'), 'score_text': format_result.get('回答标题得分'), 'max_score': format_points['标题'], 'convert_max_score': convert_format_points.get('标题', format_points['标题'])},
            {'key': '回答称谓', 'content': format_result.get('回答称谓'), 'score_text': format_result.get('回答称谓得分'), 'max_score': format_points['称谓'], 'convert_max_score': convert_format_points.get('称谓', format_points['称谓'])},
            {'key': '发文主体', 'content': signature_dict.get('发文主体'), 'score_text': signature_score_dict.get('发文主体得分'), 'max_score': format_points['发文主体'], 'convert_max_score': convert_format_points.get('发文主体', format_points['发文主体'])},
            {'key': '发文日期', 'content': signature_dict.get('发文日期'), 'score_text': signature_score_dict.get('发文日期得分'), 'max_score': format_points['发文日期'], 'convert_max_score': convert_format_points.get('发文日期', format_points['发文日期'])}
        ]
        
        for field in fields:
            content, score = cls.process_format_field(field['content'], field['score_text'], field['max_score'])
            result[field['key']] = convert_chinese_punctuation_to_english(content)
            
            # 如果折算比例不为1，进行折算
            if has_conversion and score > 0:
                if score == field['max_score']:
                    converted_score = field['convert_max_score']
                else:
                    # 折算分数
                    converted_score = cls.convert_score(score, conversion_ratio, field['convert_max_score'])
                result[f"{field['key']}得分"] = converted_score
            else:
                result[f"{field['key']}得分"] = score
        
        return result


def custom_round(value, digits=0, method='half_up'):
    """
    自定义舍入函数
    :param value: 要舍入的数字
    :param digits: 保留的小数位数
    :param method: 舍入方式，可选：
                   - 'half_up'   传统四舍五入
                   - 'up'        始终向上取整
                   - 'down'      始终向下取整
                   - 'half_even' 银行家舍入（默认）
    :return: 舍入后的 float 数值
    """
    rounding_methods = {
        'half_up': ROUND_HALF_UP,
        'up': ROUND_UP,
        'down': ROUND_DOWN,
        'half_even': ROUND_HALF_EVEN
    }

    if method not in rounding_methods:
        raise ValueError(f"不支持的舍入方式: {method}")

    rounding_mode = rounding_methods[method]
    quantize_format = Decimal("1." + "0" * digits) if digits > 0 else Decimal("1")

    return float(Decimal(str(value)).quantize(quantize_format, rounding=rounding_mode))


def get_clear_score(score_text):
    try:
        score = custom_round(float(str(score_text).strip().replace("分", "")), 2)
        return max(score, 0.0)  # 确保分数不小于0
    except Exception as e:
        logger.error(f"get_clear_score error: {e}")
        return 0


def get_format_score(score_text):
    return score_text if isinstance(score_text, (int, float)) and score_text > 0 else 0


def get_evaluation(total_score, obtained_score):
    ratio = obtained_score / total_score
    if ratio >= 0.9:
        return "本题作答情况很好。"
    elif 0.75 <= ratio < 0.9:
        return "本题作答情况较好。"
    elif 0.5 <= ratio < 0.75:
        return "本题作答情况一般。"
    else:
        return "本题作答情况较差。"


def get_accurate_content(line_result, accurate_sum, not_entirely_accurate_sum):
    """
    获取 内容是否全面，要点是否准确 内容
    """
    try:
        # 计算要点总个数
        total_points = len(line_result)
        # 计算准确要点和不完全准确要点占比
        accurate_and_not_entirely_accurate_ratio = (accurate_sum + not_entirely_accurate_sum) / total_points if total_points > 0 else 0
        # 计算准确要点占比
        accurate_ratio = accurate_sum / total_points if total_points > 0 else 0
        # 判断内容全面等级
        if accurate_and_not_entirely_accurate_ratio >= 0.8:
            content_completeness_level = "内容全面"
        elif 0.7 <= accurate_and_not_entirely_accurate_ratio < 0.8:
            content_completeness_level = "内容基本全面"
        elif 0.5 <= accurate_and_not_entirely_accurate_ratio < 0.7:
            content_completeness_level = "内容比较全面"
        else:
            content_completeness_level = "内容不够全面"
        # 判断内容要点等级
        if accurate_ratio >= 0.8:
            content_point_level = "要点准确"
        elif 0.7 <= accurate_ratio < 0.8:
            content_point_level = "要点基本全面"
        elif 0.5 <= accurate_ratio < 0.7:
            content_point_level = "要点比较全面"
        else:
            content_point_level = "要点不够全面"
        return content_completeness_level, content_point_level
    except Exception as e:
        logger.error(f"get_accurate_content error = {e}")
        return "", ""


async def check_score(data, line_result):
    student_score = 0

    # questionKeyPoint = {tmp['keyPointId']: data['keyPoint']['questionKeyPoints'] for tmp in data['keyPoint']['questionKeyPoints']}
    # 遗漏要点定位的列表
    loss_content, accurate_sum, not_entirely_accurate_sum = [], 0, 0
    # 处理每个回答要点的分数
    for index, item in enumerate(line_result):
        # 模型不稳定性兼容，有时候会返回数字，有时候返回字符串，统一转成字符串
        point_score = get_clear_score(item['要点得分'])
        keyPointId = item['keyPointId']
        # 将关键词遗漏的进行统计
        if item['回答要点分类'] == "遗漏":
            loss_content.append(keyPointId)
            # 发现模型会有将遗漏要点也给分的情况，所以当遇到遗漏时候，直接强制变成0
            point_score = 0
        elif item['回答要点分类'] == "准确":
            accurate_sum += 1
        else:
            not_entirely_accurate_sum += 1

        item["要点得分"] = point_score
        student_score += point_score
        
    student_score = custom_round(student_score, 1)
    content_completeness_level, content_point_level = get_accurate_content(line_result, accurate_sum, not_entirely_accurate_sum)

    key_point_review = f"""{content_completeness_level}
{content_point_level}"""
    keyPoint_lost = loss_content

    return student_score, key_point_review, keyPoint_lost


async def check_format_score(data, format_result):
    """
    检查格式得分
    """
    has_conversion = data['keyPoint']['hasConversion']
    format_points = data["keyPoint"]["formatPoints"] if not has_conversion else data["keyPoint"]["convertFormatPoints"]
    format_total_score = custom_round(sum(format_points.values()), 1)
    if isinstance(format_result, dict):
        format_score = custom_round(sum([val for key, val in format_result.items() if "得分" in key]), 1)
    else:
        format_score = 0
    return format_total_score, format_score


def separate_text_by_punctuation(text):
    """
    根据标点符号分离文本
    """
    return re.split(r'[,.?!;:]', text)