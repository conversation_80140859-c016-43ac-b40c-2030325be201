def check_location_structure(data):
    # 检查 materialList 字段
    if 'materialList' in data and isinstance(data['materialList'], list):
        for item in data['materialList']:
            if not isinstance(item, dict) or 'materialId' not in item or 'materialContent' not in item:
                return False, "materialList 中的元素格式有误"
    else:
        return False, "materialList 字段不存在或格式不正确"

    # 检查 answerKeys 字段
    if 'answerKeys' in data and isinstance(data['answerKeys'], list):
        for item in data['answerKeys']:
            if not isinstance(item, dict) or 'answerKeyId' not in item or 'answerKey' not in item:
                return False, "answerKeys 中的元素格式有误"
    else:
        return False, "answerKeys 字段不存在或格式不正确"

    # 检查 answerRequirement 字段
    if not ('answerRequirement' in data and isinstance(data['answerRequirement'], str)):
        return False, "answerRequirement 字段格式不正确"
    # 检查 questionType 字段
    if not ('questionType' in data and isinstance(data['questionType'], int)):
        return False, "questionType 字段格式不正确"
    # 检查 answerKeyIds 字段
    if not ('answerKeyIds' in data and isinstance(data['answerKeyIds'], str)):
        return False, "answerKeyIds 字段格式不正确"
    
    return True, "数据结构检查通过"


def check_point_value(data, point_key, id_key, content_key, has_conversion=False):
    required_fields = [id_key, content_key, "value"]
    if has_conversion:
        required_fields.append("convertValue")

    if not isinstance(data[point_key], list):
        return False, f"{point_key} 不是列表类型"
    
    for point in data[point_key]:
        for field in required_fields:
            if field not in point:
                return False, f"{point_key} 中的元素缺少字段: {field}"
            
        if not isinstance(point[id_key], int):
            return False, f"{point_key} 中的 {id_key} 不是整数类型"
        
        if not isinstance(point[content_key], str):
            return False, f"{point_key} 中的 {content_key} 不是字符串类型"
        
        if not isinstance(point["value"], list) or len(point["value"]) != 2:
            return False, f"{point_key} 中的 value 不是包含两个元素的列表"
        for val in point["value"]:
            if not isinstance(val, (int, float)):
                return False, f"{point_key} 中的 value 的元素不是数字类型"
        if point["value"][1] < 0:
            return False, f"{point_key} 中 value 的第二个元素不能小于0"
        
        if has_conversion:
            if not isinstance(point["convertValue"], list) or len(point["convertValue"]) != 2:
                return False, f"{point_key} 中的 convertValue 不是包含两个元素的列表"
            for val in point["convertValue"]:
                if not isinstance(val, (int, float)):
                    return False, f"{point_key} 中的 convertValue 的元素不是数字类型"
            if point["convertValue"][1] < 0:
                return False, f"{point_key} 中 convertValue 的第二个元素不能小于0"
    return True, f"{point_key} 结构检查通过"


def check_correction_structure(data):
    # 定义必要的顶级字段
    required_top_level_fields = [
        "answerId", "questionId", "userAnswer", "questionType",
        "version", "correctId",  "questionKeyPoints",
        "answer", "answerRequirement", "questionDeductionPoints", "score"
    ]

    # 检查顶级字段是否存在
    for field in required_top_level_fields:
        if field not in data:
            return False, f"缺少顶级字段: {field}"

    if not isinstance(data['questionType'], int):
        return False, "questionType 字段格式不正确"
    
    if not isinstance(data["userAnswer"], str):
        return False, "userAnswer 字段格式不正确"
    if not data["userAnswer"].strip():
        return False, "userAnswer 字段不能为空"
    
    if not isinstance(data["score"], (int, float)):
        return False, "score 字段格式不正确"
    
    question_conversion_ratio = data.get('questionConversionRatio', 1)
    # 检查折算比例字段
    if not isinstance(question_conversion_ratio, (int, float)):
        return False, "questionConversionRatio 字段格式不正确"
    if not question_conversion_ratio > 0:
        return False, "questionConversionRatio 字段必须大于0"

    has_conversion = question_conversion_ratio != 1

    if has_conversion:
        if not isinstance(data.get("convertScore"), (int, float)):
            return False, "convertScore 字段格式不正确"

    # 检查 questionKeyPoints 列表
    if not isinstance(data["questionKeyPoints"], list):
        return False, "questionKeyPoints 不是列表类型"
    # 非文章写作时，questionKeyPoints 列表不能为空
    if data["questionType"] != 5 and len(data["questionKeyPoints"]) == 0:
        return False, "非文章写作题型的 questionKeyPoints 列表不能为空"
    for point in data["questionKeyPoints"]:
        if not isinstance(point, dict):
            return False, "questionKeyPoints 中的元素不是字典类型"
        required_point_fields = ["keyPointContent", "keyPointId", "score", "answerLocation"]
        for field in required_point_fields:
            if field not in point:
                return False, f"questionKeyPoints 中的元素缺少字段: {field}"
        if not isinstance(point["keyPointId"], int):
            return False, "questionKeyPoints 中的 keyPointId 不是整数类型"
        if not isinstance(point["score"], (int, float)):
            return False, "questionKeyPoints 中的 score 不是数字类型"
        # 检查折算分值字段
        if has_conversion and not isinstance(point.get('convertScore'), (int, float)):
            return False, "questionKeyPoints 中的 convertScore 不是数字类型"

    # 检查 questionDeductionPoints 列表
    if not isinstance(data["questionDeductionPoints"], list) or len(data["questionDeductionPoints"]) == 0:
        return False, "questionDeductionPoints 不是列表类型 或 长度为0"
    is_valid, message = check_point_value(data, "questionDeductionPoints", "deductionPointId", "deductionPointContent", has_conversion)
    if not is_valid:
        return False, message
    
    # 文章写作字段检查
    if data["questionType"] == 5:
        # 检查必要字段
        article_fields = [
            "questionBonusPoints", "images", "centralArgument",
            "gearShiftSettings", "answerLength", "exceedingWord",
            "lowerWordLimit", "upperWordLimit", "materialList"
        ]
        for field in article_fields:
            if field not in data:
                return False, f"文章写作缺少必要字段: {field}"
            
        # 检查 questionBonusPoints 列表
        is_valid, message = check_point_value(data, "questionBonusPoints", "bonusPointId", "bonusPointContent", has_conversion)
        if not is_valid:
            return False, message

        # 检查 images（None 或列表）
        if data["images"] is not None:
            if not isinstance(data["images"], list):
                return False, "images 类型错误"
            for image in data["images"]:
                required_image_fields = ["id", "url"]
                for field in required_image_fields:
                    if field not in image:
                        return False, f"images 中的元素缺少字段: {field}"
                if not isinstance(image["id"], int):
                    return False, "images 中的元素 id 不是整数类型"
                if not isinstance(image["url"], str) or not image["url"].startswith("http"):
                    return False, "images 中的元素 url 不是http开头的字符串"
            
        # 检查 centralArgument
        if not isinstance(data["centralArgument"], str):
            return False, "centralArgument 不是字符串类型"
        
        # 检查 gearShiftSettings
        if not isinstance(data["gearShiftSettings"], list) or len(data["gearShiftSettings"]) != 4:
            return False, "gearShiftSettings 不是列表类型 或 长度不等于4"
        gear_shift_type = [1, 2, 3, 4]
        gear_shift_type_list = []
        for gear_shift in data["gearShiftSettings"]:
            required_gear_shift_fields = ["type", "wordsLower", "wordsUpper", "scoreLower", "scoreUpper"]
            for field in required_gear_shift_fields:
                if field not in gear_shift:
                    return False, f"gearShiftSettings 缺少字段: {field}"
            if not isinstance(gear_shift["type"], int) or gear_shift["type"] not in gear_shift_type:
                return False, "gearShiftSettings 中的 type 不是整数类型 或 不在1-4之间"
            gear_shift_type_list.append(gear_shift["type"])
            number_keys = ["wordsLower", "wordsUpper", "scoreLower", "scoreUpper"]
            if has_conversion:
                number_keys.append("convertScoreLower")
                number_keys.append("convertScoreUpper")
            for key in number_keys:
                if not isinstance(gear_shift[key], (int, float)):
                    return False, f"gearShiftSettings 中的 {key} 不是数字类型"
                if gear_shift[key] < 0:
                    return False, f"gearShiftSettings 中的 {key} 不能小于0"
        if set(gear_shift_type_list) != set(gear_shift_type):
            return False, "gearShiftSettings 中的 type 列表不完整"
            
        # 检查 exceedingWord
        if not isinstance(data["exceedingWord"], dict):
            return False, "exceedingWord 类型错误"
        exceeding_info = data["exceedingWord"]
        # 可选项
        if "deductionScore" in exceeding_info:
            if not isinstance(exceeding_info["deductionScore"], (int, float)):
                return False, "exceedingWord 中的 deductionScore 不是数字类型"
            if exceeding_info["deductionScore"] < 0:
                return False, "exceedingWord 中的 deductionScore 不能小于0"
            if has_conversion:
                if not isinstance(exceeding_info.get("convertDeductionScore"), (int, float)):
                    return False, "exceedingWord 中的 convertDeductionScore 不是数字类型"
                if exceeding_info.get("convertDeductionScore") < 0:
                    return False, "exceedingWord 中的 convertDeductionScore 不能小于0"
        
        # 检查 materialList
        if not isinstance(data["materialList"], list) or len(data["materialList"]) == 0:
            return False, "materialList 不是列表类型 或 长度为0"
        for material in data["materialList"]:
            required_material_fields = ["id", "content"]
            for field in required_material_fields:
                if field not in material:
                    return False, f"materialList 中的元素缺少字段: {field}"
            if not isinstance(material["id"], int):
                return False, "materialList 中的元素 id 不是整数类型"
            if not isinstance(material["content"], str):
                return False, "materialList 中的元素 content 不是字符串类型"
        
        # 检查数字类型
        int_keys = ["answerLength", "lowerWordLimit", "upperWordLimit"]
        for key in int_keys:
            if not isinstance(data[key], int):
                return False, f"{key} 不是整数类型"
            if data[key] < 0:
                return False, f"{key} 不能小于0"
        
        if data["lowerWordLimit"] > data["upperWordLimit"]:
            return False, "lowerWordLimit 大于 upperWordLimit"
        
    return True, "JSON 结构检查通过"


def check_answer_point_structure(data):
    required_fields = ["questionId", "questionType", "answer", "answerRequirement", "score", "formatScore", "contentScore", "materialList", "timestamp"]
    for field in required_fields:
        if field not in data:
            return False, f"缺少必要字段: {field}"
    
    int_keys = ["questionId", "questionType"]
    for key in int_keys:
        if not isinstance(data[key], int):
            return False, f"{key} 不是整数类型"
    
    socre_keys = ["score", "formatScore", "contentScore"]
    for key in socre_keys:
        if not isinstance(data[key], (int, float)):
            return False, f"{key} 不是整数类型或浮点数类型"
        if data[key] < 0:
            return False, f"{key} 不能小于0"
        
    str_keys = ["answer", "answerRequirement"]
    for key in str_keys:
        if not isinstance(data[key], str):
            return False, f"{key} 不是字符串类型"
    
    if not isinstance(data["materialList"], list):
        return False, "materialList 不是列表类型"
    for material in data["materialList"]:
        if not isinstance(material, dict):
            return False, "materialList 中的元素不是字典类型"
        if not isinstance(material["id"], int):
            return False, "materialList 中的元素 id 不是整数类型"
        if not isinstance(material["content"], str):
            return False, "materialList 中的元素 content 不是字符串类型"
            
    return True, "JSON 结构检查通过"